# Agent Orchestration Layer Testing Strategy

## Overview

This document outlines the comprehensive testing strategy for the agent orchestration layer. The goal is to ensure that the orchestration layer is robust, reliable, and performs well under various conditions.

## Testing Levels

### 1. Unit Testing

Unit tests focus on testing individual components in isolation, ensuring that each component works correctly on its own.

#### Components to Test

- **Agent Manager**: Tests for creating, updating, retrieving, and deleting agents
- **Tool Registry**: Tests for registering, retrieving, and managing tools
- **Execution Service**: Tests for starting, monitoring, and controlling agent executions
- **State Manager**: Tests for saving and loading execution state
- **Auth Service**: Tests for validating tokens and checking permissions
- **Telemetry Service**: Tests for recording metrics and traces
- **Recovery Service**: Tests for recovering failed executions

#### Example Unit Test

```rust
#[tokio::test]
async fn test_agent_manager_create_agent() {
    // Setup mock database and agent service client
    let db_pool = setup_test_db().await;
    let agent_service_client = MockAgentServiceClient::new();

    // Create agent manager
    let agent_manager = AgentManager::new(db_pool.clone(), Arc::new(agent_service_client));

    // Create agent config
    let agent_config = AgentConfig {
        id: Uuid::new_v4(),
        name: "Test Agent".to_string(),
        description: "Test description".to_string(),
        model: "test-model".to_string(),
        tools: vec![],
        system_prompt: "Test prompt".to_string(),
        max_iterations: 10,
        team_id: None,
        created_by: Uuid::new_v4(),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    // Call create_agent
    let result = agent_manager.create_agent(agent_config.clone()).await;

    // Assert result
    assert!(result.is_ok());
    let agent_id = result.unwrap();

    // Verify agent was stored in database
    let stored_agent = get_agent_from_db(db_pool, agent_id).await;
    assert!(stored_agent.is_some());
    let stored_agent = stored_agent.unwrap();
    assert_eq!(stored_agent.name, agent_config.name);
    assert_eq!(stored_agent.description, agent_config.description);

    // Verify agent service client was called
    assert!(agent_service_client.register_agent_called_with(agent_id));
}
```

### 2. Integration Testing

Integration tests focus on testing the interaction between components, ensuring that they work correctly together.

#### Flows to Test

- **Agent Creation and Tool Registration**: Test creating an agent and registering tools
- **Agent Execution**: Test executing an agent with various inputs
- **Tool Invocation**: Test invoking tools during agent execution
- **State Management**: Test saving and loading state during execution
- **Authentication and Authorization**: Test access control for agents and tools
- **Error Handling**: Test error scenarios and recovery mechanisms

#### Example Integration Test

```rust
#[tokio::test]
async fn test_agent_execution_flow() {
    // Setup test environment
    let (db_pool, agent_service_client, agent_manager, tool_registry, execution_service) =
        setup_test_environment().await;

    // Create agent
    let agent_config = AgentConfig {
        id: Uuid::new_v4(),
        name: "Test Agent".to_string(),
        description: "Test description".to_string(),
        model: "test-model".to_string(),
        tools: vec![],
        system_prompt: "Test prompt".to_string(),
        max_iterations: 10,
        team_id: None,
        created_by: Uuid::new_v4(),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };
    let agent_id = agent_manager.create_agent(agent_config).await.unwrap();

    // Register tool
    let tool_config = ToolConfig {
        id: Uuid::new_v4(),
        name: "test_tool".to_string(),
        description: "Test tool".to_string(),
        schema: serde_json::json!({
            "type": "object",
            "properties": {
                "param": {
                    "type": "string"
                }
            }
        }),
        handler_type: ToolHandlerType::Function,
        handler_config: serde_json::json!({
            "function": "test_function"
        }),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };
    let tool_id = tool_registry.register_tool(tool_config).await.unwrap();

    // Add tool to agent
    agent_manager.add_tool_to_agent(agent_id, tool_id).await.unwrap();

    // Execute agent
    let execution_id = execution_service.start_execution(
        agent_id,
        Uuid::new_v4(),
        "Test input".to_string(),
    ).await.unwrap();

    // Wait for execution to complete
    wait_for_execution_completion(execution_service, execution_id).await;

    // Verify execution status
    let status = execution_service.get_execution_status(execution_id).await.unwrap();
    assert_eq!(status, ExecutionStatus::Completed);

    // Verify messages
    let state_manager = StateManager::new(db_pool.clone());
    let messages = state_manager.get_history(execution_id).await.unwrap();
    assert!(!messages.is_empty());

    // Verify tool calls
    let tool_calls = get_tool_calls_for_execution(db_pool, execution_id).await;
    assert!(!tool_calls.is_empty());
}
```

### 3. End-to-End Testing

End-to-end tests focus on testing complete agent workflows, ensuring that the entire system works correctly from the user's perspective.

#### Single-Agent Scenarios to Test

- **Customer Support Agent**: Test a customer support agent handling inquiries
- **Data Analysis Agent**: Test a data analysis agent processing data
- **Research Agent**: Test a research agent gathering information

#### Multi-Agent Scenarios to Test

- **Sequential Orchestration**: Test agents working in sequence
- **Hierarchical Orchestration**: Test manager-worker agent hierarchy
- **Collaborative Orchestration**: Test agents working in parallel with a mixer
- **Competitive Orchestration**: Test agents competing with a judge
- **Diamond Pattern**: Test routing through specialists and a rephraser
- **Peer-to-Peer Orchestration**: Test agent handoffs
- **Adaptive Loop Orchestration**: Test iterative refinement

#### Example End-to-End Test

```rust
#[tokio::test]
async fn test_customer_support_agent_workflow() {
    // Setup real environment (or realistic mocks)
    let (agent_manager, tool_registry, execution_service) = setup_real_environment().await;

    // Create customer support agent with necessary tools
    let agent_id = create_customer_support_agent(agent_manager, tool_registry).await;

    // Execute agent with a customer inquiry
    let execution_id = execution_service.start_execution(
        agent_id,
        Uuid::new_v4(),
        "I haven't received my order #54321 yet. It's been 5 days.".to_string(),
    ).await.unwrap();

    // Wait for execution to complete
    wait_for_execution_completion(execution_service, execution_id).await;

    // Verify the agent's response
    let state_manager = StateManager::new(db_pool.clone());
    let messages = state_manager.get_history(execution_id).await.unwrap();

    // Find the last assistant message
    let last_assistant_message = messages.iter()
        .filter(|m| m.role == MessageRole::Assistant)
        .last()
        .unwrap();

    // Verify the response contains expected information
    assert!(last_assistant_message.content.contains("shipping delay"));
    assert!(last_assistant_message.content.contains("expected delivery date"));
}

#[tokio::test]
async fn test_hierarchical_orchestration_workflow() {
    // Setup real environment (or realistic mocks)
    let (agent_manager, tool_registry, execution_service, orchestration_service) =
        setup_real_environment().await;

    // Create manager agent
    let manager_agent_id = create_router_agent(agent_manager, tool_registry).await;

    // Create worker agents
    let order_agent_id = create_order_agent(agent_manager, tool_registry).await;
    let shipping_agent_id = create_shipping_agent(agent_manager, tool_registry).await;
    let returns_agent_id = create_returns_agent(agent_manager, tool_registry).await;

    // Create hierarchical orchestration
    let orchestration_id = orchestration_service.create_hierarchical_orchestration(
        "Customer Support Orchestration",
        "Handles customer support inquiries",
        manager_agent_id,
        vec![
            ("order", order_agent_id),
            ("shipping", shipping_agent_id),
            ("returns", returns_agent_id),
        ],
    ).await.unwrap();

    // Execute orchestration with a customer inquiry
    let execution_id = orchestration_service.execute_orchestration(
        orchestration_id,
        Uuid::new_v4(),
        "I haven't received my order #54321 yet. It's been 5 days.".to_string(),
    ).await.unwrap();

    // Wait for execution to complete
    wait_for_orchestration_completion(orchestration_service, execution_id).await;

    // Verify the orchestration result
    let result = orchestration_service.get_orchestration_result(execution_id).await.unwrap();

    // Verify the response contains expected information
    assert!(result.contains("shipping delay"));
    assert!(result.contains("expected delivery date"));

    // Verify the correct worker agent was used
    let execution_details = orchestration_service.get_orchestration_details(execution_id).await.unwrap();
    assert_eq!(execution_details.worker_agent_id, shipping_agent_id);
}
```

### 4. Performance Testing

Performance tests focus on measuring the performance of the orchestration layer under various conditions.

#### Metrics to Measure

- **Latency**: Time to complete various operations
- **Throughput**: Number of operations per second
- **Resource Usage**: CPU, memory, and network usage
- **Scalability**: Performance under increasing load

#### Example Performance Test

```rust
#[tokio::test]
async fn test_agent_execution_performance() {
    // Setup test environment
    let (agent_manager, tool_registry, execution_service) = setup_performance_test_environment().await;

    // Create agent and tools
    let agent_id = create_test_agent(agent_manager, tool_registry).await;

    // Measure execution time for different inputs
    let inputs = vec![
        "Simple query",
        "Complex query with multiple steps",
        "Query requiring multiple tool calls",
    ];

    for input in inputs {
        let start_time = Instant::now();

        let execution_id = execution_service.start_execution(
            agent_id,
            Uuid::new_v4(),
            input.to_string(),
        ).await.unwrap();

        wait_for_execution_completion(execution_service, execution_id).await;

        let elapsed = start_time.elapsed();

        println!("Input: {}, Elapsed: {:?}", input, elapsed);

        // Assert that execution time is within acceptable limits
        assert!(elapsed < Duration::from_secs(5));
    }
}
```

### 5. Load Testing

Load tests focus on testing the orchestration layer under heavy load, ensuring that it can handle many concurrent users and operations.

#### Scenarios to Test

- **Concurrent Agent Executions**: Test executing many agents concurrently
- **Concurrent Tool Invocations**: Test invoking many tools concurrently
- **Sustained Load**: Test performance under sustained load over time

#### Example Load Test

```rust
#[tokio::test]
async fn test_concurrent_agent_executions() {
    // Setup test environment
    let (agent_manager, tool_registry, execution_service) = setup_load_test_environment().await;

    // Create agent and tools
    let agent_id = create_test_agent(agent_manager, tool_registry).await;

    // Execute agent concurrently
    let num_concurrent = 100;
    let mut handles = vec![];

    for i in 0..num_concurrent {
        let execution_service = execution_service.clone();
        let agent_id = agent_id.clone();

        let handle = tokio::spawn(async move {
            let execution_id = execution_service.start_execution(
                agent_id,
                Uuid::new_v4(),
                format!("Test input {}", i),
            ).await.unwrap();

            wait_for_execution_completion(execution_service, execution_id).await;

            execution_id
        });

        handles.push(handle);
    }

    // Wait for all executions to complete
    let execution_ids = futures::future::join_all(handles).await;

    // Verify all executions completed successfully
    for execution_id in execution_ids {
        let execution_id = execution_id.unwrap();
        let status = execution_service.get_execution_status(execution_id).await.unwrap();
        assert_eq!(status, ExecutionStatus::Completed);
    }
}
```

## Testing Tools and Infrastructure

### 1. Mock Components

Mock components will be used to isolate components during unit testing:

- **MockAgentServiceClient**: Mock client for the agent service
- **MockToolRegistry**: Mock registry for tools
- **MockExecutionService**: Mock service for executions
- **MockStateManager**: Mock manager for state
- **MockAuthService**: Mock service for authentication
- **MockTelemetryService**: Mock service for telemetry

### 2. Test Database

A test database will be used for integration and end-to-end testing:

- **Setup**: Create a fresh database for each test
- **Teardown**: Drop the database after each test
- **Migrations**: Apply migrations to set up the schema
- **Seed Data**: Insert seed data for testing

### 3. Test Environment

A test environment will be set up for end-to-end testing:

- **Services**: Start all required services
- **Configuration**: Configure services for testing
- **Cleanup**: Clean up resources after testing

## Continuous Integration

Tests will be run automatically as part of the continuous integration pipeline:

- **Unit Tests**: Run on every commit
- **Integration Tests**: Run on every pull request
- **End-to-End Tests**: Run on every pull request
- **Performance Tests**: Run on a schedule
- **Load Tests**: Run on a schedule

## Test Coverage

Test coverage will be measured and monitored:

- **Goal**: Achieve at least 80% code coverage
- **Critical Paths**: Ensure 100% coverage of critical paths
- **Reports**: Generate coverage reports for each build

## Conclusion

This testing strategy provides a comprehensive approach to testing the agent orchestration layer, ensuring that it is robust, reliable, and performs well under various conditions. By following this strategy, we can have confidence in the quality of the orchestration layer and its ability to meet the requirements of the Saday Agent platform.
