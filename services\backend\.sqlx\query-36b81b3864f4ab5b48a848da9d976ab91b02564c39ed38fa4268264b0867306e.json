{"db_name": "PostgreSQL", "query": "\n            SELECT id, name, description, domain, settings, created_at, updated_at\n            FROM organizations\n            WHERE id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "description", "type_info": "Text"}, {"ordinal": 3, "name": "domain", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "settings", "type_info": "Jsonb"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>"]}, "nullable": [false, false, true, true, true, false, false]}, "hash": "36b81b3864f4ab5b48a848da9d976ab91b02564c39ed38fa4268264b0867306e"}