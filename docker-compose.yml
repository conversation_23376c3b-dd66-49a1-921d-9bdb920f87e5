version: '3.8'

services:
  # PostgreSQL service
  db:
    container_name: saday-db
    image: postgres:15
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=saday
    volumes:
      - postgres_data:/var/lib/postgresql/data
    command: ["postgres"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - saday_network


  backend:
    container_name: saday-backend
    build:
      context: ./services/backend
      args:
        DATABASE_URL: ************************************/saday
        # Set to true for faster builds, false to regenerate SQLx prepare files
        SQLX_OFFLINE: true

    environment:
      - DATABASE_URL=************************************/saday
      - JWT_SECRET_KEY=d55e5129f008bba4ec41a1c6c3a21bdc47b029f5dee4e36326b0279ab383f80f
      - JWT_MAXAGE=60
      - SMTP_SERVER=smtppro.zoho.in
      - SMTP_PORT=465
      - SMTP_USERNAME=<EMAIL>
      - SMTP_PASSWORD=Gvx1SUTYStA8
      - SMTP_FROM_ADDRESS=<EMAIL>
      - SMTP_TLS=true
      - SMTP_SSL=true
      - SMTP_TLS_REQUIRED=true
      - SMTP_AUTH_MECHANISM=Plain
      - CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://web:3000,https://api.saday.xyz,https://saday.xyz,https://saday.online,https://www.saday.online,http://localhost:8081
    ports:
      - "8001:8001"
    depends_on:
      db:
        condition: service_healthy
    restart: on-failure
    networks:
      - saday_network


volumes:
  postgres_data:
    name: saday-postgres-data

networks:
  saday_network:
    driver: bridge
  
