{"info": {"_postman_id": "bc95f19c-48d9-4ce8-836b-b1567882c760", "name": "Lola Backend API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "22799621", "description": "# Lola Backend API Documentation\n\nThis collection contains all available endpoints for the Lola backend authentication and user management system.\n\n## Authentication System\nThe Lola Backend uses a passwordless authentication system based on email OTP (One-Time Passwords) as the sole authentication method. This eliminates the need for passwords and simplifies the user experience.\n\n## Auth Handler\n- **POST /auth/request-otp**: Request an OTP code to be sent to your email. Returns `userType: \"New\"` for new users.\n- **POST /auth/verify-otp**: Login or register with the OTP code. If the email is new, a new account is created.\n\n## Users Handler\n- **GET /users/me**: Get the currently authenticated user's profile.\n- **GET /users/users**: Get a list of all users (admin only).\n- **GET /users/unverified**: Get a list of all unverified users (admin only).\n- **PUT /users/name**: Update the user's name.\n- **PUT /users/role**: Update the user's role (admin only).\n- **PUT /users/verification**: Update user verification status (admin only).\n\n### Authentication\n- Most endpoints require authentication via JWT (sent as a cookie or Authorization header).\n- Admin-only endpoints require the user to have the Admin role.\n- Some endpoints require the user to be verified by an admin (verified_admin flag).\n\n### Response Format\n- All endpoints return JSON responses.\n- Success responses typically include a \"status\" field with value \"success\".\n- Error responses include appropriate HTTP status codes and error messages.\n\n### Pagination\n- List endpoints support pagination with page and limit query parameters.\n\n### For more details, see the request/response examples in each request."}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Request OTP", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/request-otp", "host": ["{{host}}"], "path": ["auth", "request-otp"]}}, "response": [{"name": "Existing User Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/request-otp", "host": ["{{host}}"], "path": ["auth", "request-otp"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"message\": \"OTP sent to your email\",\r\n    \"userType\": \"Existing\"\r\n}"}, {"name": "New User Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/request-otp", "host": ["{{host}}"], "path": ["auth", "request-otp"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"message\": \"OTP sent to your email\",\r\n    \"userType\": \"New\",\r\n    \"signup_text\": \"Complete your registration by entering the OTP sent to your email\"\r\n}"}]}, {"name": "Verify OTP (Login/Register)", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"otp_code\": \"123456\",\r\n    \"name\": \"<PERSON>\",\r\n    \"date_of_birth\": \"1990-01-01\",\r\n    \"why_lola\": \"meditation\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/verify-otp", "host": ["{{host}}"], "path": ["auth", "verify-otp"]}}, "response": [{"name": "Existing User Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"otp_code\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/verify-otp", "host": ["{{host}}"], "path": ["auth", "verify-otp"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"token\": \"jwt_token_here\",\r\n    \"userType\": \"Existing\"\r\n}"}, {"name": "New User Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"otp_code\": \"123456\",\r\n    \"name\": \"New User\",\r\n    \"date_of_birth\": \"1990-01-01\",\r\n    \"why_lola\": \"meditation\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/verify-otp", "host": ["{{host}}"], "path": ["auth", "verify-otp"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"token\": \"jwt_token_here\",\r\n    \"userType\": \"New\",\r\n    \"message\": \"Account created successfully\"\r\n}"}]}]}, {"name": "Users Handler", "item": [{"name": "Get Me", "request": {"method": "GET", "header": [], "url": {"raw": "{{host}}/users/me", "host": ["{{host}}"], "path": ["users", "me"]}}, "response": []}, {"name": "Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{host}}/users/users", "host": ["{{host}}"], "path": ["users", "users"]}}, "response": []}, {"name": "Update User Name", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/users/name", "host": ["{{host}}"], "path": ["users", "name"]}}, "response": []}, {"name": "Update User Role", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"role\": \"Admin | User\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/users/role", "host": ["{{host}}"], "path": ["users", "role"]}}, "response": []}, {"name": "Update User Verification", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer YOUR_JWT_TOKEN", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"USER_ID_TO_VERIFY\",\n    \"verified_admin\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/users/verification", "host": ["{{host}}"], "path": ["users", "verification"]}}, "response": []}, {"name": "Get Unverified Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer YOUR_JWT_TOKEN", "type": "text"}], "url": {"raw": "{{host}}/users/unverified", "host": ["{{host}}"], "path": ["users", "unverified"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "host", "value": "http://127.0.0.1:8000/api", "type": "string"}]}