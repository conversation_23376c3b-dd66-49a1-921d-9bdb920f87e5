# Installation Guide

This guide provides step-by-step instructions for setting up the Saday Backend development environment.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Rust and Cargo**: The Rust programming language and its package manager
- **PostgreSQL**: Database server (version 12 or higher)
- **Git**: Version control system
- **System Dependencies**: Required libraries for building the project

## Installing Rust and Cargo

1. Install Rust and Cargo using rustup:

   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   ```

2. Follow the on-screen instructions to complete the installation.

3. Verify the installation:

   ```bash
   rustc --version
   cargo --version
   ```

## Installing PostgreSQL

### On Debian/Ubuntu

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
```

### On Fedora

```bash
sudo dnf install postgresql-server postgresql-contrib
sudo postgresql-setup --initdb --unit postgresql
sudo systemctl enable postgresql
sudo systemctl start postgresql
```

### On Arch Linux

```bash
sudo pacman -S postgresql
sudo -u postgres initdb -D /var/lib/postgres/data
sudo systemctl enable postgresql
sudo systemctl start postgresql
```

### On macOS (using Homebrew)

```bash
brew install postgresql
brew services start postgresql
```

## Installing System Dependencies

### On Debian/Ubuntu

```bash
sudo apt-get update
sudo apt-get install build-essential pkg-config libssl-dev
```

### On Fedora

```bash
sudo dnf install make gcc openssl-devel pkgconf-pkg-config
```

### On Arch Linux

```bash
sudo pacman -S base-devel openssl pkgconf
```

### On macOS (using Homebrew)

```bash
brew install openssl pkg-config
```

## Cloning the Repository

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/saday-backend.git
   cd saday-backend
   ```

## Setting Up the Environment

1. Create a `.env` file in the project root:

   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your configuration:

   ```
   # Database (PostgreSQL)
   DATABASE_URL=postgres://postgres:postgres@localhost:5432/saday

   # JSON Web Token Credentials
   JWT_SECRET_KEY=your_jwt_secret_key
   JWT_MAXAGE=60

   # SMTP Server Settings
   SMTP_SERVER=your_smtp_server
   SMTP_PORT=465
   SMTP_USERNAME=your_smtp_username
   SMTP_PASSWORD=your_smtp_password
   SMTP_FROM_ADDRESS=<EMAIL>
   SMTP_TLS=true
   SMTP_SSL=true
   SMTP_TLS_REQUIRED=true
   SMTP_AUTH_MECHANISM=Plain

   # Frontend and Backend URLs
   FRONTEND_URL=http://localhost:3000
   BACKEND_URL=http://localhost:8001

   # OTP settings
   OTP_EXPIRY_MINUTES=10
   OTP_MAX_ATTEMPTS=5
   OTP_RATE_LIMIT_HOURS=1
   OTP_RATE_LIMIT_MAX=100
   ```

3. Generate a secure JWT secret key:

   ```bash
   openssl rand -hex 32
   ```

   Copy the output and paste it as the value for `JWT_SECRET_KEY` in your `.env` file.

## Building the Project

1. Build the project:

   ```bash
   cargo build
   ```

## Running the Server

1. Run the server:

   ```bash
   cargo run
   ```

   The server will start on http://localhost:8001 by default.

## Troubleshooting

### Common Issues

1. **Missing Dependencies**

   If you see errors about missing `cc`, `pkg-config`, or OpenSSL, make sure you have installed the system dependencies mentioned above.

2. **Database Connection Issues**

   - Ensure PostgreSQL is running:
     ```bash
     sudo systemctl status postgresql
     ```
   - Check your database connection string in the `.env` file
   - Ensure the database exists:
     ```bash
     psql -U postgres -c "CREATE DATABASE saday;"
     ```

3. **Compilation Errors**

   - Update Rust to the latest version:
     ```bash
     rustup update
     ```
   - Clean and rebuild the project:
     ```bash
     cargo clean
     cargo build
     ```

4. **Port Already in Use**

   If port 8001 is already in use, you can change the port in the `.env` file by modifying the `BACKEND_URL` variable.

## Next Steps

After completing the installation, proceed to:

1. [Environment Configuration](./environment.md) for detailed information about environment variables
2. [Database Setup](./database.md) for setting up the database schema
3. [Testing Guide](./testing.md) for running tests
