# Tests

This directory contains automated tests for the Lola backend application.

## Test Structure

- `common/` - Common utilities and helpers for tests
- `interactive_auth_test.rs` - Interactive authentication tests

## Running Tests

### Interactive Authentication Tests

These tests require user interaction to complete:

```bash
# Run signup test
cargo test test_signup_flow -- --ignored --nocapture

# Run signin test
cargo test test_signin_flow -- --ignored --nocapture
```

You can also use the menu-driven scripts in the `scripts` directory:

```bash
# PowerShell (Windows)
.\scripts\run_tests.ps1

# Bash (Unix-like systems)
./scripts/run_tests.sh
```

### Test Utilities

The `common` directory contains shared test utilities:

- `mod.rs` - Common functions for creating test requests, extracting responses, etc.
