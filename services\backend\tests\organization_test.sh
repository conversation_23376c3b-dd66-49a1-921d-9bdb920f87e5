#!/bin/bash

# Colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Use the local server on port 8001
API_URL="http://localhost:8001/api"
print_message "${BLUE}" "Using local server at ${API_URL}"

# Test if API is accessible
if ! curl -s "${API_URL}/healthchecker" > /dev/null; then
    print_message "${RED}" "Cannot connect to ${API_URL}"
    print_message "${YELLOW}" "Make sure the server is running on port 8001."
    print_message "${YELLOW}" "You can start it with: cargo run"
    exit 1
fi

print_message "${GREEN}" "Successfully connected to API at ${API_URL}"

# Test user email
TEST_EMAIL="<EMAIL>"
TOKEN=""
ORGANIZATION_ID=""

# Function to handle errors
handle_error() {
    local message=$1
    print_message "${RED}" "ERROR: ${message}"
    exit 1
}

# Function to send OTP
send_otp() {
    print_message "${BLUE}" "Sending OTP to ${TEST_EMAIL}..."

    # Use the request-otp endpoint like in the interactive test
    response=$(curl -s -X POST "${API_URL}/auth/request-otp" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"${TEST_EMAIL}\"}")

    if [[ $response == *"success"* ]]; then
        print_message "${GREEN}" "OTP sent successfully!"
    else
        print_message "${RED}" "Failed to send OTP. Response: ${response}"

        # Try an interactive authentication approach
        print_message "${BLUE}" "Would you like to try interactive authentication instead? (y/n)"
        read -p "Enter your choice: " choice

        if [[ $choice == "y" || $choice == "Y" ]]; then
            interactive_auth
        else
            exit 1
        fi
    fi
}

# Function for interactive authentication
interactive_auth() {
    print_message "${BLUE}" "Starting interactive authentication..."
    print_message "${YELLOW}" "Please enter your authentication token (JWT):"
    print_message "${YELLOW}" "You can get this token by:"
    print_message "${YELLOW}" "1. Using the browser to log in to your application"
    print_message "${YELLOW}" "2. Using browser developer tools to extract the JWT token"
    print_message "${YELLOW}" "3. Or using the API directly with a tool like Postman"

    read -p "Token: " TOKEN

    if [[ -z "$TOKEN" ]]; then
        print_message "${RED}" "No token provided. Exiting."
        exit 1
    fi

    # Remove any quotes or extra characters that might be in the token
    TOKEN=$(echo $TOKEN | tr -d '"' | tr -d "'" | xargs)

    # Verify the token format
    if [[ ! $TOKEN =~ ^[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+$ ]]; then
        print_message "${YELLOW}" "Warning: The token doesn't look like a valid JWT (should be in format xxx.yyy.zzz)."
        print_message "${YELLOW}" "Do you want to continue anyway? (y/n)"
        read -p "Continue? " continue_choice

        if [[ $continue_choice != "y" && $continue_choice != "Y" ]]; then
            print_message "${RED}" "Exiting as requested."
            exit 1
        fi
    fi

    # Test the token with a simple request
    print_message "${BLUE}" "Testing token with a simple request..."
    test_response=$(curl -s -X GET "${API_URL}/healthchecker" \
        -H "Authorization: Bearer ${TOKEN}")

    print_message "${GREEN}" "Token received. Proceeding with the test..."
    print_message "${YELLOW}" "DEBUG: Token (first 20 chars): ${TOKEN:0:20}..."
}

# Function to verify OTP and get token
verify_otp() {
    local otp=$1

    print_message "${BLUE}" "Verifying OTP..."

    # Use the verify-otp endpoint like in the interactive test
    response=$(curl -v -X POST "${API_URL}/auth/verify-otp" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"${TEST_EMAIL}\",\"otp_code\":\"${otp}\"}" 2>&1 | tee /tmp/verify_response.log)

    # Get the response body only (remove headers)
    response=$(cat /tmp/verify_response.log | grep -v "^>" | grep -v "^<" | grep -v "^*" | tail -n 1)

    print_message "${YELLOW}" "DEBUG: Raw response: ${response}"

    if [[ $response == *"token"* ]]; then
        # Extract token directly with grep (more reliable)
        TOKEN=$(echo $response | grep -o '"token":"[^"]*' | cut -d'"' -f4)

        print_message "${GREEN}" "OTP verified successfully! Token received."
        print_message "${YELLOW}" "DEBUG: Token (first 20 chars): ${TOKEN:0:20}..."
    else
        print_message "${RED}" "Failed to verify OTP. Response: ${response}"
        print_message "${BLUE}" "Would you like to try interactive authentication instead? (y/n)"
        read -p "Enter your choice: " choice

        if [[ $choice == "y" || $choice == "Y" ]]; then
            interactive_auth
        else
            exit 1
        fi
    fi
}

# Function to get user organizations
get_user_organizations() {
    print_message "${BLUE}" "Getting user organizations..."

    # Debug token
    print_message "${YELLOW}" "DEBUG: Using token (first 20 chars): ${TOKEN:0:20}..."

    # Make a verbose request to see what's happening
    print_message "${YELLOW}" "DEBUG: Making request to ${API_URL}/organizations"

    # Print the exact authorization header for debugging
    AUTH_HEADER="Authorization: Bearer ${TOKEN}"
    print_message "${YELLOW}" "DEBUG: Using auth header: ${AUTH_HEADER:0:40}..."

    # Try with different header formats
    print_message "${YELLOW}" "DEBUG: Trying with standard Bearer format..."
    response=$(curl -v -X GET "${API_URL}/organizations" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}" 2>&1 | tee /tmp/orgs_response.log)

    # Get the response body only (remove headers)
    response=$(cat /tmp/orgs_response.log | grep -v "^>" | grep -v "^<" | grep -v "^*" | tail -n 1)

    print_message "${YELLOW}" "DEBUG: Raw response: ${response}"

    if [[ $response == *"organizations"* ]]; then
        print_message "${GREEN}" "Organizations retrieved successfully!"

        # Check if there are any organizations
        if command -v jq &> /dev/null; then
            results=$(echo $response | jq -r '.results')
        else
            results=$(echo $response | grep -o '"results":[0-9]*' | cut -d':' -f2)
        fi

        if [[ $results -gt 0 ]]; then
            print_message "${YELLOW}" "User has ${results} organization(s)."

            # Get the first organization ID
            if command -v jq &> /dev/null; then
                ORGANIZATION_ID=$(echo $response | jq -r '.organizations[0].id')
            else
                ORGANIZATION_ID=$(echo $response | grep -o '"id":"[^"]*' | head -1 | cut -d'"' -f4)
            fi

            print_message "${YELLOW}" "Selected organization ID: ${ORGANIZATION_ID}"

            # Get organization details
            get_organization_details
        else
            print_message "${YELLOW}" "User has no organizations. Creating a new one..."
            create_organization
        fi
    else
        print_message "${RED}" "Failed to get organizations. Response: ${response}"

        # Try with a different authorization header format
        print_message "${YELLOW}" "DEBUG: Trying with alternative header format..."

        # Try with just the cookie
        print_message "${YELLOW}" "DEBUG: Trying with just the cookie..."
        alt_response=$(curl -v -X GET "${API_URL}/organizations" \
            --cookie "token=${TOKEN}" 2>&1 | tee /tmp/orgs_alt_response.log)

        alt_response=$(cat /tmp/orgs_alt_response.log | grep -v "^>" | grep -v "^<" | grep -v "^*" | tail -n 1)

        if [[ $alt_response == *"organizations"* ]]; then
            print_message "${GREEN}" "Success with alternative header format!"
            response=$alt_response
            # Continue with the successful response
            print_message "${GREEN}" "Organizations retrieved successfully!"

            # Process the response...
            # (rest of the success handling code)
        else
            # Try to fix the token format if it looks wrong
            if [[ $TOKEN == *"\"token\""* ]]; then
                print_message "${YELLOW}" "DEBUG: Token appears to be in JSON format. Extracting..."
                TOKEN=$(echo $TOKEN | grep -o '"token":"[^"]*' | cut -d'"' -f4)
                print_message "${YELLOW}" "DEBUG: Extracted token (first 20 chars): ${TOKEN:0:20}..."

                # Try again with the fixed token
                print_message "${BLUE}" "Trying again with fixed token format..."
                get_user_organizations
            else
                # Try one more approach - check the API endpoint
                print_message "${YELLOW}" "DEBUG: Checking if we're using the correct API endpoint..."

                # Try with /api/users/me to see if authentication works
                print_message "${YELLOW}" "DEBUG: Trying /users/me with both header and cookie..."
                me_response=$(curl -s -X GET "${API_URL}/users/me" \
                    -H "Authorization: Bearer ${TOKEN}" \
                    --cookie "token=${TOKEN}")

                print_message "${YELLOW}" "DEBUG: /users/me response: ${me_response}"

                handle_error "Failed to get organizations. Response: ${response}"
            fi
        fi
    fi
}

# Function to create an organization
create_organization() {
    print_message "${BLUE}" "Creating a new organization..."

    local org_name="Test Organization $(date +%s)"

    response=$(curl -s -X POST "${API_URL}/organizations" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        --cookie "token=${TOKEN}" \
        -d "{\"name\":\"${org_name}\",\"description\":\"Test organization created via CLI test\"}")

    if [[ $response == *"success"* ]]; then
        ORGANIZATION_ID=$(echo $response | grep -o '"id":"[^"]*' | head -1 | cut -d'"' -f4)
        print_message "${GREEN}" "Organization created successfully! ID: ${ORGANIZATION_ID}"

        # Get organization details
        get_organization_details
    else
        handle_error "Failed to create organization. Response: ${response}"
    fi
}

# Function to get organization details
get_organization_details() {
    print_message "${BLUE}" "Getting organization details..."

    response=$(curl -s -X GET "${API_URL}/organizations/${ORGANIZATION_ID}" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}")

    if [[ $response == *"success"* ]]; then
        org_name=$(echo $response | grep -o '"name":"[^"]*' | head -1 | cut -d'"' -f4)
        print_message "${GREEN}" "Organization details retrieved successfully!"
        print_message "${YELLOW}" "Organization name: ${org_name}"

        # Get organization members
        get_organization_members
    else
        handle_error "Failed to get organization details. Response: ${response}"
    fi
}

# Function to get organization members
get_organization_members() {
    print_message "${BLUE}" "Getting organization members..."

    response=$(curl -s -X GET "${API_URL}/organizations/${ORGANIZATION_ID}/members" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}")

    if [[ $response == *"members"* ]]; then
        print_message "${GREEN}" "Organization members retrieved successfully!"

        # Check if there are any members
        results=$(echo $response | grep -o '"results":[0-9]*' | cut -d':' -f2)

        print_message "${YELLOW}" "Organization has ${results} member(s)."

        # Ask if user wants to invite a new member
        read -p "Do you want to invite a new member? (y/n): " invite_choice

        if [[ $invite_choice == "y" || $invite_choice == "Y" ]]; then
            invite_member
        else
            print_message "${YELLOW}" "Test completed successfully!"
        fi
    else
        handle_error "Failed to get organization members. Response: ${response}"
    fi
}

# Function to invite a member
invite_member() {
    print_message "${BLUE}" "Inviting a new member..."

    read -p "Enter email address to invite: " invite_email

    response=$(curl -s -X POST "${API_URL}/organizations/${ORGANIZATION_ID}/members" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        --cookie "token=${TOKEN}" \
        -d "{\"email\":\"${invite_email}\",\"role\":\"user\"}")

    if [[ $response == *"success"* ]]; then
        print_message "${GREEN}" "Invitation sent successfully to ${invite_email}!"
        print_message "${YELLOW}" "Test completed successfully!"
    else
        handle_error "Failed to send invitation. Response: ${response}"
    fi
}

# Main execution
print_message "${BLUE}" "=== Organization Feature Test ==="

# Ask user which authentication method to use
print_message "${YELLOW}" "Choose authentication method:"
print_message "${YELLOW}" "1. Interactive authentication (paste JWT token)"
print_message "${YELLOW}" "2. OTP authentication (email: ${TEST_EMAIL})"
read -p "Enter your choice (1 or 2): " auth_choice

if [[ $auth_choice == "1" ]]; then
    # Use interactive authentication
    interactive_auth
else
    # Use OTP authentication
    print_message "${YELLOW}" "This test will use the email: ${TEST_EMAIL}"

    # Send OTP
    send_otp

    # Ask for OTP
    read -p "Enter the OTP received at ${TEST_EMAIL}: " otp

    # Verify OTP
    verify_otp $otp
fi

# Get user organizations
get_user_organizations

print_message "${BLUE}" "=== Test Completed ==="
