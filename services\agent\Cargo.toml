[package]
name = "agentai"
version = "0.1.3"
edition = "2021"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/AdamStrojek/rust-agentai"
description = "AgentAI is a Rust library designed to simplify the creation of AI agents."
readme = "README.md"
categories = ["text-processing"]
keywords = ["generative-ai", "openai", "chatgpt", "gemini", "agent"]
exclude = ["release-plz.toml", ".github"]

[package.metadata.docs.rs]
cargo-args = ["-Zunstable-options", "-Zrustdoc-scrape-examples"]

[dependencies]
genai = "0.1.23"
anyhow = "1.0.95"
serde_json = "1.0.134"
async-trait = "0.1.83"
log = "0.4.22"
schemars = "0.8.21"
serde = "1.0.216"
reqwest = "0.12.12"
mcp_client_rs = {version = "0.1.7", optional = true}
document-features = { version = "0.2"}

[dev-dependencies]
tokio = { version = "1.42.0", features = ["full"] }
simplelog = "0.12.2"

[features]
default = ["mcp-client"]
#! Available features for `agentai` crate.
#! To enable any of these features, you need to enter this command:
#!
#! ```bash
#! cargo add agentai -F mcp-client
#! ```
#!
#! Features list:

## Enables experimental support for Agent Tools based on MCP Servers
mcp-client = ["dep:mcp_client_rs"]
