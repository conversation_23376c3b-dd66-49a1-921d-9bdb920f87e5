# Test Lola OTP Email Preview Script

# Function to replace placeholders in a template
function Replace-Placeholders {
    param (
        [string]$templateContent,
        [hashtable]$placeholders
    )

    foreach ($key in $placeholders.Keys) {
        $templateContent = $templateContent -replace $key, $placeholders[$key]
    }

    return $templateContent
}

# Create output directory if it doesn't exist
$previewDir = "email_previews"
if (-not (Test-Path $previewDir)) {
    New-Item -ItemType Directory -Path $previewDir | Out-Null
    Write-Host "Created preview directory: $previewDir"
}

# Get the current directory and construct absolute paths
$currentDir = (Get-Location).Path
$logoBlackPath = Join-Path -Path $currentDir -ChildPath "images\LolaLogoBlack.png"
$logoWhitePath = Join-Path -Path $currentDir -ChildPath "images\LolaLogoWhite.png"

# Convert paths to file:// URLs for HTML
$logoBlackUrl = "file:///" + $logoBlackPath.Replace("\", "/")
$logoWhiteUrl = "file:///" + $logoWhitePath.Replace("\", "/")

Write-Host "Logo Black Path: $logoBlackPath"
Write-Host "Logo White Path: $logoWhitePath"

# Sample data for OTP template
$otpCode = "123456"
$otpDigits = $otpCode.ToCharArray()

$otpPlaceholders = @{
    "{{username}}" = "John Doe"
    "{{otp_code}}" = $otpCode
    "{{digit_1}}" = $otpDigits[0]
    "{{digit_2}}" = $otpDigits[1]
    "{{digit_3}}" = $otpDigits[2]
    "{{digit_4}}" = $otpDigits[3]
    "{{digit_5}}" = $otpDigits[4]
    "{{digit_6}}" = $otpDigits[5]
    "cid:LolaLogoBlack" = $logoBlackUrl
    "cid:LolaLogoWhite" = $logoWhiteUrl
}

# Process the new Lola OTP template
Write-Host "Processing Lola OTP template..." -ForegroundColor Cyan
$templatePath = "src/mail/templates/lola-OTP-email.html"

if (Test-Path $templatePath) {
    $content = Get-Content -Path $templatePath -Raw
    $processedContent = Replace-Placeholders -templateContent $content -placeholders $otpPlaceholders
    $outputPath = "$previewDir/lola_otp_preview.html"
    Set-Content -Path $outputPath -Value $processedContent
    Write-Host "Preview saved to: $outputPath" -ForegroundColor Green

    # Open the preview in the default browser
    Write-Host "Opening preview in browser..." -ForegroundColor Cyan
    Start-Process $outputPath
} else {
    Write-Host "Template not found: $templatePath" -ForegroundColor Red
}

Write-Host "`nPreview file is saved in the '$previewDir' directory."
Write-Host "The preview has been opened in your browser."
