{"info": {"_postman_id": "bc95f19c-48d9-4ce8-836b-b1567882c760", "name": "Saday Backend API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "22799621", "description": "# Saday Backend API Documentation\n\nThis collection contains all available endpoints for the Saday backend authentication, user management, and organization system.\n\n## Authentication System\nThe Saday Backend uses a passwordless authentication system based on email OTP (One-Time Passwords) as the sole authentication method. This eliminates the need for passwords and simplifies the user experience.\n\n## Auth Handler\n- **POST /auth/request-otp**: Request an OTP code to be sent to your email. Returns `userType: \"New\"` for new users.\n- **POST /auth/verify-otp**: Login or register with the OTP code. If the email is new, a new account is created.\n- **POST /auth/resend-otp**: Resend an OTP to the specified email.\n- **POST /auth/reset-account**: Request an account reset.\n- **POST /auth/verify-reset**: Verify account reset with OTP.\n\n## Users Handler\n- **GET /users/me**: Get the currently authenticated user's profile.\n- **GET /users/users**: Get a list of all users (admin only).\n- **PUT /users/profile**: Update the user's profile information.\n- **PUT /users/role**: Update the user's role (admin only).\n\n## Organization Handler\n- **POST /organizations**: Create a new organization.\n- **GET /organizations**: Get all organizations for the current user.\n- **GET /organizations/:id**: Get a specific organization by ID.\n- **PUT /organizations/:id**: Update an organization.\n- **DELETE /organizations/:id**: Delete an organization.\n- **GET /organizations/:id/members**: Get all members of an organization.\n- **POST /organizations/:id/members**: Invite a new member to an organization.\n- **PUT /organizations/:id/members/:member_id**: Update a member's role.\n- **DELETE /organizations/:id/members/:member_id**: Remove a member from an organization.\n- **POST /organizations/invitations/accept**: Accept an organization invitation.\n- **GET /organizations/invitations**: Get all invitations for the current user.\n- **GET /organizations/:id/invitations**: Get all invitations for an organization.\n- **PUT /organizations/default/:id**: Set the default organization for the current user.\n\n### Authentication\n- All endpoints (except auth endpoints) require authentication via JWT (sent as a cookie or Authorization header).\n- Admin-only endpoints require the user to have the Admin role.\n- Organization endpoints require appropriate organization roles.\n\n### Response Format\n- All endpoints return JSON responses with a standard format.\n- Success responses include a \"status\" field with value \"success\" and either a \"data\" field or a \"message\" field.\n- Error responses include a \"status\" field with value \"error\" and a \"message\" field.\n\n### Pagination\n- List endpoints support pagination with page and limit query parameters."}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{host}}/healthchecker", "host": ["{{host}}"], "path": ["healthchecker"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{host}}/healthchecker", "host": ["{{host}}"], "path": ["healthchecker"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"message\": \"API is running\"\r\n}"}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Request OTP", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/request-otp", "host": ["{{host}}"], "path": ["auth", "request-otp"]}}, "response": [{"name": "Existing User Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/request-otp", "host": ["{{host}}"], "path": ["auth", "request-otp"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"message\": \"OTP sent to your email\",\r\n    \"userType\": \"Existing\"\r\n}"}, {"name": "New User Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/request-otp", "host": ["{{host}}"], "path": ["auth", "request-otp"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"message\": \"OTP sent to your email\",\r\n    \"userType\": \"New\",\r\n    \"signup_text\": \"Complete your registration by entering the OTP sent to your email\"\r\n}"}]}, {"name": "Verify OTP (Login/Register)", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"otp_code\": \"123456\",\r\n    \"name\": \"<PERSON>\",\r\n    \"profile_data\": {\r\n        \"bio\": \"Software developer\",\r\n        \"location\": \"San Francisco\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/verify-otp", "host": ["{{host}}"], "path": ["auth", "verify-otp"]}}, "response": [{"name": "Existing User Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"otp_code\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/verify-otp", "host": ["{{host}}"], "path": ["auth", "verify-otp"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"token\": \"jwt_token_here\",\r\n    \"userType\": \"Existing\"\r\n}"}, {"name": "New User Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"otp_code\": \"123456\",\r\n    \"name\": \"New User\",\r\n    \"profile_data\": {\r\n        \"bio\": \"Software developer\",\r\n        \"location\": \"San Francisco\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/verify-otp", "host": ["{{host}}"], "path": ["auth", "verify-otp"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"token\": \"jwt_token_here\",\r\n    \"userType\": \"New\",\r\n    \"message\": \"Account created successfully\"\r\n}"}]}, {"name": "Resend OTP", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/resend-otp", "host": ["{{host}}"], "path": ["auth", "resend-otp"]}}, "response": []}, {"name": "Reset Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/reset-account", "host": ["{{host}}"], "path": ["auth", "reset-account"]}}, "response": []}, {"name": "Verify Reset", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"otp_code\": \"123456\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/auth/verify-reset", "host": ["{{host}}"], "path": ["auth", "verify-reset"]}}, "response": []}]}, {"name": "Users Handler", "item": [{"name": "Get Me", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/users/me", "host": ["{{host}}"], "path": ["users", "me"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/users/me", "host": ["{{host}}"], "path": ["users", "me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"data\": {\r\n        \"user\": {\r\n            \"id\": \"9669208d-b352-4258-bf59-e9b4a543fa16\",\r\n            \"name\": \"<PERSON>\",\r\n            \"email\": \"<EMAIL>\",\r\n            \"role\": \"user\",\r\n            \"verified\": true,\r\n            \"verified_admin\": false,\r\n            \"profile_data\": {\r\n                \"bio\": \"Software developer\",\r\n                \"location\": \"San Francisco\"\r\n            },\r\n            \"createdAt\": \"2023-01-01T00:00:00Z\",\r\n            \"updatedAt\": \"2023-01-01T00:00:00Z\"\r\n        }\r\n    }\r\n}"}]}, {"name": "Get Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/users/users?page=1&limit=10", "host": ["{{host}}"], "path": ["users", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/users/users?page=1&limit=10", "host": ["{{host}}"], "path": ["users", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"users\": [\r\n        {\r\n            \"id\": \"9669208d-b352-4258-bf59-e9b4a543fa16\",\r\n            \"name\": \"<PERSON>\",\r\n            \"email\": \"<EMAIL>\",\r\n            \"role\": \"user\",\r\n            \"verified\": true,\r\n            \"verified_admin\": false,\r\n            \"createdAt\": \"2023-01-01T00:00:00Z\",\r\n            \"updatedAt\": \"2023-01-01T00:00:00Z\"\r\n        },\r\n        {\r\n            \"id\": \"9669208d-b352-4258-bf59-e9b4a543fa17\",\r\n            \"name\": \"<PERSON>\",\r\n            \"email\": \"<EMAIL>\",\r\n            \"role\": \"admin\",\r\n            \"verified\": true,\r\n            \"verified_admin\": true,\r\n            \"createdAt\": \"2023-01-02T00:00:00Z\",\r\n            \"updatedAt\": \"2023-01-02T00:00:00Z\"\r\n        }\r\n    ],\r\n    \"results\": 2\r\n}"}]}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Updated Name\",\r\n    \"profile_data\": {\r\n        \"bio\": \"Updated bio\",\r\n        \"location\": \"Paris, France\",\r\n        \"preferences\": {\r\n            \"theme\": \"dark\",\r\n            \"language\": \"en\"\r\n        }\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/users/profile", "host": ["{{host}}"], "path": ["users", "profile"]}}, "response": []}, {"name": "Update User Role", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"user_id\": \"9669208d-b352-4258-bf59-e9b4a543fa16\",\r\n    \"role\": \"admin\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/users/role", "host": ["{{host}}"], "path": ["users", "role"]}}, "response": []}]}, {"name": "Organization Handler", "item": [{"name": "Create Organization", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"My Organization\",\r\n    \"description\": \"This is my organization\",\r\n    \"domain\": \"example.com\",\r\n    \"settings\": {\r\n        \"color\": \"#FF5733\",\r\n        \"logo\": \"https://example.com/logo.png\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/organizations", "host": ["{{host}}"], "path": ["organizations"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"My Organization\",\r\n    \"description\": \"This is my organization\",\r\n    \"domain\": \"example.com\",\r\n    \"settings\": {\r\n        \"color\": \"#FF5733\",\r\n        \"logo\": \"https://example.com/logo.png\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/organizations", "host": ["{{host}}"], "path": ["organizations"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"data\": {\r\n        \"id\": \"123e4567-e89b-12d3-a456-426614174000\",\r\n        \"name\": \"My Organization\",\r\n        \"description\": \"This is my organization\",\r\n        \"domain\": \"example.com\",\r\n        \"settings\": {\r\n            \"color\": \"#FF5733\",\r\n            \"logo\": \"https://example.com/logo.png\"\r\n        },\r\n        \"createdAt\": \"2023-01-01T00:00:00Z\",\r\n        \"updatedAt\": \"2023-01-01T00:00:00Z\"\r\n    }\r\n}"}]}, {"name": "Get User Organizations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/organizations?page=1&limit=10", "host": ["{{host}}"], "path": ["organizations"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/organizations?page=1&limit=10", "host": ["{{host}}"], "path": ["organizations"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\r\n    \"status\": \"success\",\r\n    \"organizations\": [\r\n        {\r\n            \"id\": \"123e4567-e89b-12d3-a456-426614174000\",\r\n            \"name\": \"My Organization\",\r\n            \"description\": \"This is my organization\",\r\n            \"domain\": \"example.com\",\r\n            \"settings\": {\r\n                \"color\": \"#FF5733\",\r\n                \"logo\": \"https://example.com/logo.png\"\r\n            },\r\n            \"role\": \"orgadmin\",\r\n            \"createdAt\": \"2023-01-01T00:00:00Z\",\r\n            \"updatedAt\": \"2023-01-01T00:00:00Z\"\r\n        },\r\n        {\r\n            \"id\": \"123e4567-e89b-12d3-a456-426614174001\",\r\n            \"name\": \"Another Organization\",\r\n            \"description\": \"This is another organization\",\r\n            \"domain\": \"another-example.com\",\r\n            \"settings\": {\r\n                \"color\": \"#33FF57\",\r\n                \"logo\": \"https://example.com/another-logo.png\"\r\n            },\r\n            \"role\": \"orguser\",\r\n            \"createdAt\": \"2023-01-02T00:00:00Z\",\r\n            \"updatedAt\": \"2023-01-02T00:00:00Z\"\r\n        }\r\n    ],\r\n    \"results\": 2\r\n}"}]}, {"name": "Get Organization", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/organizations/:id", "host": ["{{host}}"], "path": ["organizations", ":id"], "variable": [{"key": "id", "value": "123e4567-e89b-12d3-a456-426614174000"}]}}, "response": []}, {"name": "Update Organization", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Updated Organization Name\",\r\n    \"description\": \"This is an updated description\",\r\n    \"domain\": \"updated-example.com\",\r\n    \"settings\": {\r\n        \"color\": \"#33FF57\",\r\n        \"logo\": \"https://example.com/updated-logo.png\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/organizations/:id", "host": ["{{host}}"], "path": ["organizations", ":id"], "variable": [{"key": "id", "value": "123e4567-e89b-12d3-a456-426614174000"}]}}, "response": []}, {"name": "Delete Organization", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/organizations/:id", "host": ["{{host}}"], "path": ["organizations", ":id"], "variable": [{"key": "id", "value": "123e4567-e89b-12d3-a456-426614174000"}]}}, "response": []}, {"name": "Get Organization Members", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/organizations/:id/members?page=1&limit=10", "host": ["{{host}}"], "path": ["organizations", ":id", "members"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}], "variable": [{"key": "id", "value": "123e4567-e89b-12d3-a456-426614174000"}]}}, "response": []}, {"name": "Invite Member", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"role\": \"orguser\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/organizations/:id/members", "host": ["{{host}}"], "path": ["organizations", ":id", "members"], "variable": [{"key": "id", "value": "123e4567-e89b-12d3-a456-426614174000"}]}}, "response": []}, {"name": "Update Member Role", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"role\": \"orgeditor\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/organizations/:id/members/:member_id", "host": ["{{host}}"], "path": ["organizations", ":id", "members", ":member_id"], "variable": [{"key": "id", "value": "123e4567-e89b-12d3-a456-426614174000"}, {"key": "member_id", "value": "123e4567-e89b-12d3-a456-426614174001"}]}}, "response": []}, {"name": "Remove Member", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/organizations/:id/members/:member_id", "host": ["{{host}}"], "path": ["organizations", ":id", "members", ":member_id"], "variable": [{"key": "id", "value": "123e4567-e89b-12d3-a456-426614174000"}, {"key": "member_id", "value": "123e4567-e89b-12d3-a456-426614174001"}]}}, "response": []}, {"name": "Accept Invitation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"invitation_token_here\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/organizations/invitations/accept", "host": ["{{host}}"], "path": ["organizations", "invitations", "accept"]}}, "response": []}, {"name": "Get User Invitations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/organizations/invitations", "host": ["{{host}}"], "path": ["organizations", "invitations"]}}, "response": []}, {"name": "Get Organization Invitations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/organizations/:id/invitations", "host": ["{{host}}"], "path": ["organizations", ":id", "invitations"], "variable": [{"key": "id", "value": "123e4567-e89b-12d3-a456-426614174000"}]}}, "response": []}, {"name": "Set Default Organization", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{host}}/organizations/default/:id", "host": ["{{host}}"], "path": ["organizations", "default", ":id"], "variable": [{"key": "id", "value": "123e4567-e89b-12d3-a456-426614174000"}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "host", "value": "http://localhost:8001/api", "type": "string"}]}