#!/bin/bash

# Colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Use the local server on port 8001
API_URL="http://localhost:8001/api"
print_message "${BLUE}" "Using local server at ${API_URL}"

# Test if API is accessible
if ! curl -s "${API_URL}/healthchecker" > /dev/null; then
    print_message "${RED}" "Cannot connect to ${API_URL}"
    print_message "${YELLOW}" "Make sure the server is running on port 8001."
    print_message "${YELLOW}" "You can start it with: cargo run"
    exit 1
fi

print_message "${GREEN}" "Successfully connected to API at ${API_URL}"
print_message "${BLUE}" "=== Authentication Debug Test ==="

# Test user email
TEST_EMAIL="<EMAIL>"

# Function to handle errors
handle_error() {
    print_message "${RED}" "ERROR: $1"
    exit 1
}

# Function to send OTP
send_otp() {
    print_message "${BLUE}" "Sending OTP to ${TEST_EMAIL}..."
    
    # Use the request-otp endpoint
    response=$(curl -s -X POST "${API_URL}/auth/request-otp" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"${TEST_EMAIL}\"}")

    if [[ $response == *"success"* ]]; then
        print_message "${GREEN}" "OTP sent successfully!"
    else
        handle_error "Failed to send OTP. Response: ${response}"
    fi
}

# Function to verify OTP and get token
verify_otp() {
    local otp=$1
    
    print_message "${BLUE}" "Verifying OTP..."
    
    # Use the verify-otp endpoint
    response=$(curl -v -X POST "${API_URL}/auth/verify-otp" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"${TEST_EMAIL}\",\"otp_code\":\"${otp}\"}" 2>&1)
    
    # Extract the token from the response
    if [[ $response == *"token"* ]]; then
        TOKEN=$(echo "$response" | grep -o '"token":"[^"]*' | cut -d'"' -f4)
        print_message "${GREEN}" "OTP verified successfully! Token received."
        print_message "${YELLOW}" "DEBUG: Token (first 20 chars): ${TOKEN:0:20}..."
        
        # Extract the Set-Cookie header if present
        COOKIE=$(echo "$response" | grep -i "set-cookie:" | head -1)
        if [[ ! -z "$COOKIE" ]]; then
            print_message "${YELLOW}" "DEBUG: Cookie received: ${COOKIE}"
        else
            print_message "${YELLOW}" "DEBUG: No cookie received in response"
        fi
        
        # Test the token with /users/me endpoint
        test_token
    else
        handle_error "Failed to verify OTP. Response: ${response}"
    fi
}

# Function to test the token
test_token() {
    print_message "${BLUE}" "Testing token with /users/me endpoint..."
    
    # Try different authentication methods
    
    # 1. Authorization header only
    print_message "${YELLOW}" "DEBUG: Trying with Authorization header only..."
    response1=$(curl -s -X GET "${API_URL}/users/me" \
        -H "Authorization: Bearer ${TOKEN}")
    print_message "${YELLOW}" "DEBUG: Response: ${response1}"
    
    # 2. Cookie only
    print_message "${YELLOW}" "DEBUG: Trying with cookie only..."
    response2=$(curl -s -X GET "${API_URL}/users/me" \
        --cookie "token=${TOKEN}")
    print_message "${YELLOW}" "DEBUG: Response: ${response2}"
    
    # 3. Both header and cookie
    print_message "${YELLOW}" "DEBUG: Trying with both header and cookie..."
    response3=$(curl -s -X GET "${API_URL}/users/me" \
        -H "Authorization: Bearer ${TOKEN}" \
        --cookie "token=${TOKEN}")
    print_message "${YELLOW}" "DEBUG: Response: ${response3}"
    
    # 4. Try with a different header format
    print_message "${YELLOW}" "DEBUG: Trying with 'Authorization: ${TOKEN}' (no Bearer)..."
    response4=$(curl -s -X GET "${API_URL}/users/me" \
        -H "Authorization: ${TOKEN}")
    print_message "${YELLOW}" "DEBUG: Response: ${response4}"
    
    # Check if any of the methods worked
    if [[ $response1 == *"success"* || $response2 == *"success"* || $response3 == *"success"* || $response4 == *"success"* ]]; then
        print_message "${GREEN}" "Authentication successful with at least one method!"
    else
        print_message "${RED}" "All authentication methods failed."
        
        # Try to inspect the token
        print_message "${YELLOW}" "DEBUG: Token inspection:"
        print_message "${YELLOW}" "DEBUG: Token length: ${#TOKEN}"
        print_message "${YELLOW}" "DEBUG: Token parts: $(echo $TOKEN | tr '.' '\n' | wc -l)"
        
        # Check if the token is properly formatted (should have 3 parts separated by dots)
        if [[ $(echo $TOKEN | tr '.' '\n' | wc -l) -ne 3 ]]; then
            print_message "${RED}" "Token does not appear to be a valid JWT (should have 3 parts separated by dots)"
        fi
    fi
}

# Main execution
send_otp

# Ask for OTP
read -p "Enter the OTP received at ${TEST_EMAIL}: " otp

# Verify OTP
verify_otp $otp

print_message "${BLUE}" "=== Test Completed ==="
