# PowerShell script to clean node_modules directories

Write-Host "Cleaning node_modules directories..." -ForegroundColor Yellow

# Clean root node_modules
if (Test-Path "node_modules") {
    Write-Host "Removing root node_modules..." -ForegroundColor Cyan
    Remove-Item -Recurse -Force "node_modules"
}

# Clean agent-native node_modules
if (Test-Path "agent-native\node_modules") {
    Write-Host "Removing agent-native\node_modules..." -ForegroundColor Cyan
    Remove-Item -Recurse -Force "agent-native\node_modules"
}

# Clean agent-web node_modules
if (Test-Path "agent-web\node_modules") {
    Write-Host "Removing agent-web\node_modules..." -ForegroundColor Cyan
    Remove-Item -Recurse -Force "agent-web\node_modules"
}

# Clean packages/apatiteui node_modules
if (Test-Path "packages\apatiteui\node_modules") {
    Write-Host "Removing packages\apatiteui\node_modules..." -ForegroundColor Cyan
    Remove-Item -Recurse -Force "packages\apatiteui\node_modules"
}

Write-Host "Cleaning complete!" -ForegroundColor Green
