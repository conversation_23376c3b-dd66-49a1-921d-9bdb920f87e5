{"db_name": "PostgreSQL", "query": "\n            UPDATE organizations\n            SET name = $1, description = $2, domain = $3, settings = $4, updated_at = NOW()\n            WHERE id = $5\n            RETURNING id, name, description, domain, settings, created_at, updated_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "description", "type_info": "Text"}, {"ordinal": 3, "name": "domain", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "settings", "type_info": "Jsonb"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "Text", "<PERSON><PERSON><PERSON><PERSON>", "Jsonb", "<PERSON><PERSON>"]}, "nullable": [false, false, true, true, true, true, true]}, "hash": "76371dc98c9c7bb3f667790e7b20194136dd0668213686727dbf187e07194ec5"}