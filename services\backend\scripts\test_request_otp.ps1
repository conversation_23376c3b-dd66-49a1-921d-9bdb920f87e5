$headers = @{
    "Content-Type" = "application/json"
}

$body = @{
    "email" = "<EMAIL>"
} | ConvertTo-Json

Write-Host "Testing /auth/request-otp endpoint with a new email..."
$response = Invoke-RestMethod -Uri "http://localhost:8001/api/auth/request-otp" -Method Post -Headers $headers -Body $body
$response | ConvertTo-Json

# Save the response for later use
$response | ConvertTo-Json | Out-File -FilePath "otp_response.json"

Write-Host "Check your database for the OTP code that was generated."
