# Module Documentation

This section contains detailed documentation for each module in the Saday Backend.

## Contents

- [Authentication Module](./auth/README.md) - Email OTP-based authentication system
- [Users Module](./users/README.md) - User management and profiles
- [Organization Module](./organization/README.md) - Organization management and team collaboration
- [Agent Module](./agent/README.md) - AI agent functionality

## Module Structure

Each module in the Saday Backend follows a similar structure:

1. **Handler** - Contains the HTTP request handlers
2. **Models** - Defines the database models
3. **DTOs** - Data Transfer Objects for API requests and responses
4. **Services** - Business logic implementation
5. **Tests** - Unit and integration tests

## Module Dependencies

The modules have the following dependencies:

- **Authentication Module** - Core module that other modules depend on
- **Users Module** - Depends on Authentication Module
- **Organization Module** - Depends on Users Module
- **Agent Module** - Depends on Users and Organization Modules

## Common Patterns

Across all modules, we follow these common patterns:

### Error Handling

All modules use the `HttpError` type for error handling, which provides consistent error responses across the API.

### Authentication and Authorization

Authentication is handled by the JWT middleware, which extracts the user from the token and makes it available to the handlers.

Authorization is handled by role-based middleware, which checks if the user has the required role to access the endpoint.

### Database Access

All modules use the `AppState` struct to access the database client, which provides a consistent interface for database operations.

### Response Format

All modules use the same response format for API responses, which includes a `status` field and either a `data` or `message` field.

## Module-Specific Documentation

For detailed information about each module, refer to the module-specific documentation:

- [Authentication Module](./auth/README.md)
- [Users Module](./users/README.md)
- [Organization Module](./organization/README.md)
- [Agent Module](./agent/README.md)
