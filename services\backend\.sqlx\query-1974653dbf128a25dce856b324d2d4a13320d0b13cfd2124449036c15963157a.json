{"db_name": "PostgreSQL", "query": "\n            SELECT COUNT(*)\n            FROM otp_requests\n            WHERE email = $1\n            AND request_time > NOW() - INTERVAL '1 hour' * $2\n            ", "describe": {"columns": [{"ordinal": 0, "name": "count", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Float8"]}, "nullable": [null]}, "hash": "1974653dbf128a25dce856b324d2d4a13320d0b13cfd2124449036c15963157a"}