# Agent Service Integration Plan

## Overview

This document outlines the plan for integrating the agent service with the main backend. The integration will enable seamless communication between the two services, allowing the main backend to manage agent configurations, execute agents, and retrieve results.

## Current State

### Main Backend
- Built with Rust using Axum framework
- Uses PostgreSQL with SQLx for database access
- Implements JWT-based authentication
- Provides RESTful API endpoints

### Agent Service
- Currently named "agentai"
- Built on the AgentAI library
- Supports various AI models
- Includes tool registration and execution
- Needs integration with the main backend's authentication and database

## Integration Goals

1. **Rename and adapt the agent service**
   - Rename from "agentai" to "saday-agent"
   - Update repository information and metadata
   - Add to the main Cargo workspace

2. **Implement database schema changes**
   - Create tables for agents, tools, executions, etc.
   - Implement migration scripts

3. **Extend authentication**
   - Integrate JWT validation with agent service
   - Implement team-based access control

4. **Create API endpoints**
   - Implement agent management endpoints
   - Implement tool management endpoints
   - Implement execution management endpoints

5. **Develop client interface**
   - Create client for communication between services
   - Implement error handling and retries

6. **Implement multi-agent orchestration**
   - Design and implement orchestration patterns
   - Create agent communication protocols
   - Develop contract-based approach

## Implementation Plan

### Week 1: Foundation

#### Day 1-2: Agent Service Adaptation
- [ ] Rename package in Cargo.toml
- [ ] Update version, authors, and repository information
- [ ] Add to main Cargo workspace
- [ ] Update README and documentation

#### Day 3-5: Database Schema Implementation
- [ ] Design database schema for agent-related tables
- [ ] Create SQLx migration scripts
- [ ] Implement database access layer
- [ ] Write tests for database operations

### Week 2: Core Integration

#### Day 1-2: Authentication Integration
- [ ] Implement JWT validation in agent service
- [ ] Create middleware for authentication
- [ ] Implement team-based access control
- [ ] Write tests for authentication

#### Day 3-5: API Endpoint Implementation
- [ ] Implement agent management endpoints
- [ ] Implement tool management endpoints
- [ ] Implement execution management endpoints
- [ ] Write tests for API endpoints

### Week 3: Client and Multi-Agent Orchestration

#### Day 1-2: Client Interface Development
- [ ] Create client for communication between services
- [ ] Implement error handling and retries
- [ ] Add logging and telemetry
- [ ] Write tests for client interface

#### Day 3-5: Multi-Agent Orchestration Implementation
- [ ] Implement sequential orchestration pattern
- [ ] Implement hierarchical orchestration pattern
- [ ] Implement collaborative orchestration pattern
- [ ] Implement other orchestration patterns as needed
- [ ] Create agent communication protocols
- [ ] Develop contract-based approach for complex tasks

### Week 4: Testing and Refinement

#### Day 1-3: Integration Testing
- [ ] Write integration tests for single-agent flows
- [ ] Write integration tests for multi-agent flows
- [ ] Test error scenarios and edge cases
- [ ] Benchmark performance
- [ ] Fix issues and optimize

#### Day 4-5: Documentation and Refinement
- [ ] Document multi-agent orchestration patterns
- [ ] Create examples for each pattern
- [ ] Refine API based on testing feedback
- [ ] Optimize performance bottlenecks

## Technical Details

### Database Schema

The following tables will be added to the main database:

```sql
-- Agents table
CREATE TABLE agents (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    model VARCHAR(255) NOT NULL,
    system_prompt TEXT NOT NULL,
    max_iterations INTEGER NOT NULL DEFAULT 10,
    team_id UUID REFERENCES teams(id),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Tools table
CREATE TABLE tools (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    schema JSONB NOT NULL,
    handler_type VARCHAR(50) NOT NULL,
    handler_config JSONB NOT NULL,
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    team_id UUID REFERENCES teams(id),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Agent tools mapping
CREATE TABLE agent_tools (
    agent_id UUID NOT NULL REFERENCES agents(id),
    tool_id UUID NOT NULL REFERENCES tools(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (agent_id, tool_id)
);

-- Executions table
CREATE TABLE executions (
    id UUID PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id),
    user_id UUID NOT NULL REFERENCES users(id),
    status VARCHAR(50) NOT NULL,
    input TEXT NOT NULL,
    output TEXT,
    error TEXT,
    metadata JSONB,
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Multi-agent orchestrations table
CREATE TABLE orchestrations (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    pattern VARCHAR(50) NOT NULL,
    config JSONB NOT NULL,
    team_id UUID REFERENCES teams(id),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Orchestration agents mapping
CREATE TABLE orchestration_agents (
    orchestration_id UUID NOT NULL REFERENCES orchestrations(id),
    agent_id UUID NOT NULL REFERENCES agents(id),
    role VARCHAR(50) NOT NULL,
    config JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (orchestration_id, agent_id)
);

-- Agent contracts table
CREATE TABLE agent_contracts (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    inputs JSONB NOT NULL,
    outputs JSONB NOT NULL,
    constraints JSONB,
    validation_criteria JSONB,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Agent contract implementations
CREATE TABLE agent_contract_implementations (
    contract_id UUID NOT NULL REFERENCES agent_contracts(id),
    agent_id UUID NOT NULL REFERENCES agents(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY (contract_id, agent_id)
);
```

### API Endpoints

The following API endpoints will be implemented:

#### Agent Management
- `POST /api/agents` - Create a new agent
- `GET /api/agents` - List agents
- `GET /api/agents/{id}` - Get agent by ID
- `PUT /api/agents/{id}` - Update agent
- `DELETE /api/agents/{id}` - Delete agent

#### Tool Management
- `POST /api/tools` - Register a new tool
- `GET /api/tools` - List tools
- `GET /api/tools/{id}` - Get tool by ID
- `PUT /api/tools/{id}` - Update tool
- `DELETE /api/tools/{id}` - Delete tool

#### Execution Management
- `POST /api/agents/{id}/execute` - Execute agent
- `GET /api/executions/{id}` - Get execution status
- `GET /api/executions/{id}/messages` - Get execution messages
- `POST /api/executions/{id}/abort` - Abort execution

#### Orchestration Management
- `POST /api/orchestrations` - Create a new orchestration
- `GET /api/orchestrations` - List orchestrations
- `GET /api/orchestrations/{id}` - Get orchestration by ID
- `PUT /api/orchestrations/{id}` - Update orchestration
- `DELETE /api/orchestrations/{id}` - Delete orchestration
- `POST /api/orchestrations/{id}/execute` - Execute orchestration
- `GET /api/orchestrations/{id}/agents` - Get agents in orchestration
- `POST /api/orchestrations/{id}/agents` - Add agent to orchestration
- `DELETE /api/orchestrations/{id}/agents/{agent_id}` - Remove agent from orchestration

#### Contract Management
- `POST /api/contracts` - Create a new contract
- `GET /api/contracts` - List contracts
- `GET /api/contracts/{id}` - Get contract by ID
- `PUT /api/contracts/{id}` - Update contract
- `DELETE /api/contracts/{id}` - Delete contract
- `POST /api/contracts/{id}/implementations` - Add agent implementation
- `GET /api/contracts/{id}/implementations` - Get agent implementations
- `DELETE /api/contracts/{id}/implementations/{agent_id}` - Remove agent implementation

### Client Interface

The client interface will be implemented as follows:

```rust
pub struct AgentServiceClient {
    base_url: String,
    client: reqwest::Client,
    jwt_token: String,
}

impl AgentServiceClient {
    pub fn new(base_url: String, jwt_token: String) -> Self {
        Self {
            base_url,
            client: reqwest::Client::new(),
            jwt_token,
        }
    }

    pub async fn register_agent(&self, agent_config: AgentConfig) -> Result<(), AgentServiceError> {
        let url = format!("{}/api/agents", self.base_url);

        let response = self.client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.jwt_token))
            .json(&agent_config)
            .send()
            .await?;

        if response.status().is_success() {
            Ok(())
        } else {
            let error_text = response.text().await?;
            Err(AgentServiceError::ApiError(error_text))
        }
    }

    // Other methods for agent management, tool management, and execution
}
```

## Testing Strategy

### Unit Tests

Unit tests will be written for each component:

- Database access layer
- API endpoints
- Client interface
- Authentication middleware

### Integration Tests

Integration tests will cover the complete flow:

- Creating an agent
- Registering tools
- Adding tools to an agent
- Executing an agent
- Retrieving execution results

### Performance Tests

Performance tests will measure:

- Latency of API calls
- Throughput of agent executions
- Resource usage under load

## Risks and Mitigations

| Risk | Mitigation |
|------|------------|
| Performance bottlenecks | Implement caching, optimize database queries, use connection pooling |
| Authentication failures | Comprehensive testing, graceful error handling, clear error messages |
| Data consistency issues | Use transactions, implement validation, add database constraints |
| API compatibility | Version API endpoints, implement backward compatibility, document changes |

## Success Criteria

The integration will be considered successful when:

1. All API endpoints are implemented and tested
2. Authentication works seamlessly between services
3. Agents can be created, configured, and executed
4. Tools can be registered and used by agents
5. Performance meets the requirements
6. All tests pass
