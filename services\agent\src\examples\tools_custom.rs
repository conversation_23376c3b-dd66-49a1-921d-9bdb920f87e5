//! # Custom Agent Tool Implementation
//!
//! This example is pretty similar to [Tools Search](crate::examples::tools_search), but here we focus
//! on demonstrating how to create your own tool that can be used later in your AI Agent
//!
//! To run this example from the terminal, enter:
//! ```bash
//! cargo run --example tools_custom
//! ```
//!
//! ## Source Code
//!
//! ```rust
#![doc = include_str!("../../examples/tools_custom.rs")]
//! ```
