# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.3](https://github.com/AdamStrojek/rust-agentai/compare/v0.1.2...v0.1.3) - 2025-01-14

### Other

- Chore/MCP Client tools and documentation ([#10](https://github.com/AdamStrojek/rust-agentai/pull/10))
- Feature/MCP Support ([#9](https://github.com/AdamStrojek/rust-agentai/pull/9))
- Chore/CI ([#8](https://github.com/AdamStrojek/rust-agentai/pull/8))

## [0.1.2](https://github.com/AdamStrojek/rust-agentai/compare/v0.1.1...v0.1.2) - 2025-01-05

### Other

- Fix links in README
- Update documentation (#7)
- *(example)* Custom Tool Example (#6)
- Implement Web Search Tool + add examples
- update documentation for Agent structure
- clippy updates
- convert tabs into spaces

## [0.1.1](https://github.com/AdamStrojek/rust-agentai/compare/v0.1.0...v0.1.1) - 2024-12-29

### Fixed

- Fix behaviour when return String type

### Other

- *(example)* New example for structured output
- Add documentation for examples, update simple example
- Update documentation
- Move AgentTool to separate module

## 0.1.0

### Added

- Initial release
- Simple example demonstrating functionality
