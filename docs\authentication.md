# Authentication System

## Overview

Saday Agent uses a passwordless authentication system based on email One-Time Passwords (OTP) as the primary authentication method. This approach:

1. Eliminates the need for users to remember passwords
2. Reduces security risks associated with password storage
3. Simplifies the user experience for both registration and login

## Database Schema

The authentication system uses a flexible schema with JSONB for storing user data:

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role user_role NOT NULL DEFAULT 'user',
    email_otp VARCHAR(6),
    email_otp_expires_at TIMESTAMPTZ,
    profile_data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

The `profile_data` field is a flexible JSONB field that can store any user-related data without requiring schema changes. Common fields include:

- `date_of_birth`: String in YYYY-MM-DD format
- `why_lola`: String ("meditation" | "journaling" | "both")
- `interests`: Array of strings
- `preferences`: Object containing user preferences
  - `theme`: String ("light" | "dark")
  - `notifications`: Boolean
- `bio`: String
- `location`: String
- `social_links`: Object containing social media links

## Authentication Flow

### Registration/Login Process

1. **Request OTP**
   - User submits their email address
   - System generates a 6-digit OTP
   - OTP is sent to the user's email
   - System stores the OTP and its expiration time (typically 10 minutes)
   - If the email is new, a placeholder user record is created with "New User" as the name

2. **Verify OTP**
   - User submits the OTP received via email
   - System validates the OTP against the stored value
   - If valid and not expired, authentication proceeds
   - For new users, additional information can be provided in the profile_data object
   - If no name is provided for new users, a default name "User" is assigned
   - A JWT token is generated and returned to the client

3. **Token Usage**
   - The JWT token is stored as an HTTP-only cookie
   - The token is included in subsequent API requests
   - Token expiration is typically set to 60 minutes (configurable)
