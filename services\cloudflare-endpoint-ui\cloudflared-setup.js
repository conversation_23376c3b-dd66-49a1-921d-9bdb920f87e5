const express = require("express");
const { exec } = require("child_process");
const util = require("util");
const fs = require("fs").promises;
const path = require("path");
const bodyParser = require("body-parser");

const execPromise = util.promisify(exec);

const app = express();
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());

// Check if cloudflared is installed
async function checkCloudflaredInstalled() {
  try {
    const { stdout } = await execPromise("cloudflared --version");
    return {
      installed: true,
      version: stdout.trim()
    };
  } catch (error) {
    return {
      installed: false,
      error: error.message
    };
  }
}

// Get list of tunnels
async function listTunnels() {
  try {
    const { stdout } = await execPromise("cloudflared tunnel list --output json");
    return JSON.parse(stdout);
  } catch (error) {
    console.error("Error listing tunnels:", error);
    return [];
  }
}

// Start a tunnel
async function startTunnel(tunnelId) {
  try {
    // Run in background and redirect output to a log file
    exec(`cloudflared tunnel run ${tunnelId} > cloudflared.log 2>&1 &`);
    return {
      success: true,
      message: `Started tunnel ${tunnelId}`
    };
  } catch (error) {
    return {
      success: false,
      message: `Error starting tunnel: ${error.message}`
    };
  }
}

// Check if a tunnel is running
async function checkTunnelRunning(tunnelId) {
  try {
    const { stdout } = await execPromise("ps aux | grep cloudflared");
    return {
      running: stdout.includes(`tunnel run ${tunnelId}`),
      processes: stdout.split('\n').filter(line => line.includes('cloudflared') && !line.includes('grep'))
    };
  } catch (error) {
    return {
      running: false,
      error: error.message
    };
  }
}

app.get("/", async (req, res) => {
  const cloudflaredStatus = await checkCloudflaredInstalled();
  const tunnels = cloudflaredStatus.installed ? await listTunnels() : [];
  
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Cloudflared Manager</title>
      <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .card { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 15px; }
        .success { color: green; }
        .error { color: red; }
        button { background: #0070f3; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; margin-top: 10px; }
        button:hover { background: #0051a2; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .info { background: #e6f7ff; border-left: 4px solid #1890ff; padding: 10px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <h1>Cloudflared Tunnel Manager</h1>
      
      <div class="card">
        <h2>Cloudflared Status</h2>
        ${cloudflaredStatus.installed 
          ? `<p class="success">✅ Cloudflared is installed: ${cloudflaredStatus.version}</p>` 
          : `<p class="error">❌ Cloudflared is not installed. <a href="https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation" target="_blank">Installation instructions</a></p>`
        }
      </div>
      
      ${cloudflaredStatus.installed ? `
        <div class="card">
          <h2>Your Tunnels</h2>
          ${tunnels.length > 0 ? `
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr>
                  <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Name</th>
                  <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">ID</th>
                  <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Created</th>
                  <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Actions</th>
                </tr>
              </thead>
              <tbody>
                ${tunnels.map(tunnel => `
                  <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${tunnel.name}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${tunnel.id}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${new Date(tunnel.created_at).toLocaleString()}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">
                      <form action="/start-tunnel" method="POST">
                        <input type="hidden" name="tunnelId" value="${tunnel.id}">
                        <button type="submit">Start Tunnel</button>
                      </form>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          ` : `<p>No tunnels found. Create a tunnel with <code>cloudflared tunnel create &lt;name&gt;</code></p>`}
        </div>
        
        <div class="card">
          <h2>Running Tunnels</h2>
          <div id="running-tunnels">Loading...</div>
          <script>
            // Fetch running tunnels every 5 seconds
            function updateRunningTunnels() {
              fetch('/running-tunnels')
                .then(response => response.json())
                .then(data => {
                  const container = document.getElementById('running-tunnels');
                  if (data.processes && data.processes.length > 0) {
                    container.innerHTML = '<ul>' + 
                      data.processes.map(process => '<li>' + process + '</li>').join('') +
                      '</ul>';
                  } else {
                    container.innerHTML = '<p>No tunnels currently running</p>';
                  }
                })
                .catch(error => {
                  console.error('Error fetching running tunnels:', error);
                });
            }
            
            updateRunningTunnels();
            setInterval(updateRunningTunnels, 5000);
          </script>
        </div>
        
        <div class="info">
          <h3>Quick Start Guide</h3>
          <ol>
            <li>Create a tunnel: <code>cloudflared tunnel create &lt;name&gt;</code></li>
            <li>Configure your tunnel: <code>cloudflared tunnel route dns &lt;tunnel-id&gt; &lt;hostname&gt;</code></li>
            <li>Start your tunnel: Click "Start Tunnel" above or run <code>cloudflared tunnel run &lt;tunnel-id&gt;</code></li>
          </ol>
        </div>
      ` : ''}
    </body>
    </html>
  `);
});

app.get("/running-tunnels", async (req, res) => {
  const status = await checkTunnelRunning("");
  res.json(status);
});

app.post("/start-tunnel", async (req, res) => {
  const { tunnelId } = req.body;
  
  if (!tunnelId) {
    return res.status(400).send("Tunnel ID is required");
  }
  
  const result = await startTunnel(tunnelId);
  
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Start Tunnel</title>
      <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .card { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 15px; }
        .success { color: green; }
        .error { color: red; }
        a { color: #0070f3; text-decoration: none; }
        a:hover { text-decoration: underline; }
      </style>
    </head>
    <body>
      <h1>Tunnel Status</h1>
      
      <div class="card">
        <h2 class="${result.success ? 'success' : 'error'}">${result.success ? 'Success' : 'Error'}</h2>
        <p>${result.message}</p>
        
        <p>The tunnel process has been started in the background. It may take a few moments to establish the connection.</p>
        
        <p>You can check the log file at: <code>cloudflared.log</code></p>
      </div>
      
      <a href="/">← Back to Tunnel Manager</a>
    </body>
    </html>
  `);
});

const PORT = process.env.PORT || 1902;
app.listen(PORT, () => {
  console.log(`Cloudflared Manager running at http://localhost:${PORT}`);
});
