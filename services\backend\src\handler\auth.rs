use std::sync::Arc;

use axum::{http::{header, HeaderMap, StatusCode}, response::IntoResponse, routing::post, Extension, Json, Router};
use axum_extra::extract::cookie::<PERSON><PERSON>;
use chrono::{Utc, Duration};
use validator::Validate;

use crate::{db::UserExt, dtos::{RequestOtpDto, RequestOtpResponseDto, ResendOtpDto, ResetAccountRequestDto, ResetAccountResponseDto, ResetAccountVerifyDto, VerifyOtpDto, VerifyOtpResponseDto}, error::HttpError, mail::mails::{send_otp_email, send_welcome_email}, utils::{otp, token}, AppState};

pub fn auth_handler() -> Router {
    Router::new()
        .route("/request-otp", post(request_otp))
        .route("/verify-otp", post(verify_otp))
        .route("/resend-otp", post(resend_otp))
        .route("/reset-account", post(request_reset_account))
        .route("/verify-reset", post(verify_reset_account))
}

pub async fn request_otp(
    Extension(app_state): Extension<Arc<AppState>>,
    Json(body): Json<RequestOtpDto>,
) -> Result<impl IntoResponse, HttpError> {
    // We'll use a default empty headers map since we can't get it directly
    let headers = HeaderMap::new();
    // Validate input
    body.validate().map_err(|e| HttpError::bad_request(e.to_string()))?;

    // Get IP address from headers if available
    let ip_address = headers
        .get("x-forwarded-for")
        .and_then(|h| h.to_str().ok())
        .or_else(|| headers.get("x-real-ip").and_then(|h| h.to_str().ok()));

    // Check rate limit (100 requests per hour)
    let rate_limited = app_state.db_client
        .check_otp_rate_limit(&body.email, 100, 1) // 100 requests per 1 hour
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if rate_limited {
        return Err(HttpError::too_many_requests(
            "Too many OTP requests. Please try again later.".to_string()
        ));
    }

    // Record this OTP request for rate limiting
    app_state.db_client
        .record_otp_request(&body.email, ip_address)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Clean up old OTP requests (older than 24 hours)
    if let Err(e) = app_state.db_client.clear_old_otp_requests(24).await {
        eprintln!("Failed to clear old OTP requests: {}", e);
    }

    // Check if the email exists
    let email_exists = app_state.db_client
        .check_email_exists(&body.email)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Generate a random OTP code
    let otp_code = otp::generate_email_otp();

    // Set OTP expiration time based on environment settings
    let expires_at = Utc::now() + Duration::minutes(app_state.env.otp_expiry_minutes);

    if email_exists {
        // Get the user to get their name
        let user = app_state.db_client
            .get_user(None, None, Some(&body.email))
            .await
            .map_err(|e| HttpError::server_error(e.to_string()))?
            .ok_or(HttpError::server_error("User not found".to_string()))?;

        // Store the OTP in the database
        app_state.db_client
            .set_email_otp(&body.email, &otp_code, expires_at)
            .await
            .map_err(|e| HttpError::server_error(e.to_string()))?;

        // Send the OTP email
        let email_result = send_otp_email(&body.email, &user.name, &otp_code).await;
        if let Err(e) = email_result {
            eprintln!("Failed to send OTP email: {}", e);
            return Err(HttpError::server_error("Failed to send OTP email".to_string()));
        }

        // Return success response for existing user
        Ok(Json(RequestOtpResponseDto {
            status: "success".to_string(),
            message: "OTP sent to your email".to_string(),
            userType: "Existing".to_string(),
            signup_text: None,
        }))
    } else {
        // For new users, we'll create a temporary entry in the database
        // with just the email and OTP

        // Create a temporary user with just the email
        let name = "New User"; // Temporary name
        let _user = app_state.db_client
            .save_user(name, &body.email)
            .await
            .map_err(|e| HttpError::server_error(e.to_string()))?;

        // Store the OTP in the database
        app_state.db_client
            .set_email_otp(&body.email, &otp_code, expires_at)
            .await
            .map_err(|e| HttpError::server_error(e.to_string()))?;

        // Send the OTP email with a generic name
        let email_result = send_otp_email(&body.email, name, &otp_code).await;
        if let Err(e) = email_result {
            eprintln!("Failed to send OTP email: {}", e);
            return Err(HttpError::server_error("Failed to send OTP email".to_string()));
        }

        // Return success response for new user
        Ok(Json(RequestOtpResponseDto {
            status: "success".to_string(),
            message: "OTP sent to your email".to_string(),
            userType: "New".to_string(),
            signup_text: Some("Complete your registration by entering the OTP sent to your email".to_string()),
        }))
    }
}

pub async fn verify_otp(
    Extension(app_state): Extension<Arc<AppState>>,
    Json(body): Json<VerifyOtpDto>
) -> Result<impl IntoResponse, HttpError> {
    // Validate input
    body.validate().map_err(|e| HttpError::bad_request(e.to_string()))?;

    // Verify the OTP
    let is_valid = app_state.db_client
        .verify_email_otp(&body.email, &body.otp_code)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if !is_valid {
        return Err(HttpError::bad_request("Invalid OTP code".to_string()));
    }

    // Get the user
    let user = app_state.db_client
        .get_user(None, None, Some(&body.email))
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or(HttpError::server_error("User not found".to_string()))?;

    // Check if this is a new user (name is "New User")
    let is_new_user = user.name == "New User";

    if is_new_user {
        // For new users, we'll use the provided name or a default
        let name = body.name.unwrap_or_else(|| "User".to_string());

        // Update the user's name and profile data
        let user = app_state.db_client
            .update_new_user(user.id, &name, body.profile_data.clone())
            .await
            .map_err(|e| HttpError::server_error(e.to_string()))?;

        // Generate JWT token
        let token = token::create_token(
            &user.id.to_string(),
            &app_state.env.jwt_secret.as_bytes(),
            app_state.env.jwt_maxage
        )
        .map_err(|e| HttpError::server_error(e.to_string()))?;

        // Set cookie
        let cookie_duration = time::Duration::minutes(app_state.env.jwt_maxage * 60);
        let cookie = Cookie::build(("token", token.clone()))
            .path("/")
            .max_age(cookie_duration)
            .http_only(true)
            .build();

        let mut headers = HeaderMap::new();
        headers.append(
            header::SET_COOKIE,
            cookie.to_string().parse().unwrap()
        );

        // Send welcome email
        let welcome_email_result = send_welcome_email(&body.email, &name).await;
        if let Err(e) = welcome_email_result {
            eprintln!("Failed to send welcome email: {}", e);
        }

        // Return success response for new user
        let response = axum::response::Json(VerifyOtpResponseDto {
            status: "success".to_string(),
            token,
            userType: "New".to_string(),
            message: Some("Account created successfully".to_string()),
        });

        Ok((StatusCode::CREATED, headers, response).into_response())
    } else {
        // Generate JWT token
        let token = token::create_token(
            &user.id.to_string(),
            &app_state.env.jwt_secret.as_bytes(),
            app_state.env.jwt_maxage
        )
        .map_err(|e| HttpError::server_error(e.to_string()))?;

        // Set cookie
        let cookie_duration = time::Duration::minutes(app_state.env.jwt_maxage * 60);
        let cookie = Cookie::build(("token", token.clone()))
            .path("/")
            .max_age(cookie_duration)
            .http_only(true)
            .build();

        let mut headers = HeaderMap::new();
        headers.append(
            header::SET_COOKIE,
            cookie.to_string().parse().unwrap()
        );

        // Return success response for existing user
        let response = axum::response::Json(VerifyOtpResponseDto {
            status: "success".to_string(),
            token,
            userType: "Existing".to_string(),
            message: None,
        });

        Ok((StatusCode::OK, headers, response).into_response())
    }
}



pub async fn resend_otp(
    Extension(app_state): Extension<Arc<AppState>>,
    Json(body): Json<ResendOtpDto>,
) -> Result<impl IntoResponse, HttpError> {
    // We'll use a default empty headers map since we can't get it directly
    let headers = HeaderMap::new();
    // Validate input
    body.validate().map_err(|e| HttpError::bad_request(e.to_string()))?;

    // Get IP address from headers if available
    let ip_address = headers
        .get("x-forwarded-for")
        .and_then(|h| h.to_str().ok())
        .or_else(|| headers.get("x-real-ip").and_then(|h| h.to_str().ok()));

    // Check rate limit based on environment settings
    let rate_limited = app_state.db_client
        .check_otp_rate_limit(
            &body.email,
            app_state.env.otp_rate_limit_max,
            app_state.env.otp_rate_limit_window_hours
        )
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if rate_limited {
        return Err(HttpError::too_many_requests(
            "Too many OTP requests. Please try again later.".to_string()
        ));
    }

    // Record this OTP request for rate limiting
    app_state.db_client
        .record_otp_request(&body.email, ip_address)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Check if the email exists
    let email_exists = app_state.db_client
        .check_email_exists(&body.email)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if !email_exists {
        return Err(HttpError::bad_request("Email not found".to_string()));
    }

    // Get the user to get their name
    let user = app_state.db_client
        .get_user(None, None, Some(&body.email))
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or(HttpError::server_error("User not found".to_string()))?;

    // Generate a random OTP code
    let otp_code = otp::generate_email_otp();

    // Set OTP expiration time based on environment settings
    let expires_at = Utc::now() + Duration::minutes(app_state.env.otp_expiry_minutes);

    // Store the OTP in the database
    app_state.db_client
        .set_email_otp(&body.email, &otp_code, expires_at)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Send the OTP email
    let email_result = send_otp_email(&body.email, &user.name, &otp_code).await;
    if let Err(e) = email_result {
        eprintln!("Failed to send OTP email: {}", e);
        return Err(HttpError::server_error("Failed to send OTP email".to_string()));
    }

    // Return success response
    Ok(Json(RequestOtpResponseDto {
        status: "success".to_string(),
        message: "OTP resent to your email".to_string(),
        userType: "Existing".to_string(),
        signup_text: None,
    }))
}

// Handler for requesting account reset
pub async fn request_reset_account(
    Extension(app_state): Extension<Arc<AppState>>,
    Json(body): Json<ResetAccountRequestDto>,
) -> Result<impl IntoResponse, HttpError> {
    // Validate input
    body.validate().map_err(|e| HttpError::bad_request(e.to_string()))?;

    // We'll use a default empty headers map since we can't get it directly
    let headers = HeaderMap::new();

    // Get IP address from headers if available
    let ip_address = headers
        .get("x-forwarded-for")
        .and_then(|h| h.to_str().ok())
        .or_else(|| headers.get("x-real-ip").and_then(|h| h.to_str().ok()));

    // Check rate limit based on environment settings
    let rate_limited = app_state.db_client
        .check_otp_rate_limit(
            &body.email,
            app_state.env.otp_rate_limit_max,
            app_state.env.otp_rate_limit_window_hours
        )
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if rate_limited {
        return Err(HttpError::too_many_requests(
            "Too many OTP requests. Please try again later.".to_string()
        ));
    }

    // Record this OTP request for rate limiting
    app_state.db_client
        .record_otp_request(&body.email, ip_address)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Check if the email exists
    let email_exists = app_state.db_client
        .check_email_exists(&body.email)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if !email_exists {
        return Err(HttpError::bad_request("Email not found".to_string()));
    }

    // Get the user to get their name
    let user = app_state.db_client
        .get_user(None, None, Some(&body.email))
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or(HttpError::server_error("User not found".to_string()))?;

    // Generate a random OTP code
    let otp_code = otp::generate_email_otp();

    // Set OTP expiration time based on environment settings
    let expires_at = Utc::now() + Duration::minutes(app_state.env.otp_expiry_minutes);

    // Store the OTP in the database
    app_state.db_client
        .set_email_otp(&body.email, &otp_code, expires_at)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Send the OTP email
    let email_result = send_otp_email(&body.email, &user.name, &otp_code).await;
    if let Err(e) = email_result {
        eprintln!("Failed to send OTP email: {}", e);
        return Err(HttpError::server_error("Failed to send OTP email".to_string()));
    }

    // Return success response
    Ok(Json(ResetAccountResponseDto {
        status: "success".to_string(),
        message: "OTP sent to your email for account reset".to_string(),
    }))
}

// Handler for verifying account reset
pub async fn verify_reset_account(
    Extension(app_state): Extension<Arc<AppState>>,
    Json(body): Json<ResetAccountVerifyDto>,
) -> Result<impl IntoResponse, HttpError> {
    // Validate input
    body.validate().map_err(|e| HttpError::bad_request(e.to_string()))?;

    // Verify the OTP
    let is_valid = app_state.db_client
        .verify_email_otp(&body.email, &body.otp_code)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if !is_valid {
        return Err(HttpError::bad_request("Invalid OTP code".to_string()));
    }

    // Reset the user account
    let _user = app_state.db_client
        .reset_user_account(&body.email)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Return success response
    Ok(Json(ResetAccountResponseDto {
        status: "success".to_string(),
        message: "Account has been reset successfully. You can now log in as a new user.".to_string(),
    }))
}


