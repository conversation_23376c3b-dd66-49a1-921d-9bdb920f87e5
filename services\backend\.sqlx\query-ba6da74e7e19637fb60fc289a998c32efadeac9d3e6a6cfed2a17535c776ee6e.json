{"db_name": "PostgreSQL", "query": "\n            INSERT INTO organizations (name, description, domain, settings)\n            VALUES ($1, $2, $3, $4)\n            RETURNING id, name, description, domain, settings, created_at, updated_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "description", "type_info": "Text"}, {"ordinal": 3, "name": "domain", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "settings", "type_info": "Jsonb"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "Text", "<PERSON><PERSON><PERSON><PERSON>", "Jsonb"]}, "nullable": [false, false, true, true, true, false, false]}, "hash": "ba6da74e7e19637fb60fc289a998c32efadeac9d3e6a6cfed2a17535c776ee6e"}