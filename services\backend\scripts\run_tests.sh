#!/bin/bash

# Display menu
echo "=== Authentication Tests ==="
echo "1. Run signup test"
echo "2. Run signin test"
echo "3. Exit"
echo ""

read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo "Running signup test..."
        cargo test test_signup_flow -- --ignored --nocapture
        ;;
    2)
        echo "Running signin test..."
        cargo test test_signin_flow -- --ignored --nocapture
        ;;
    3)
        echo "Exiting..."
        exit 0
        ;;
    *)
        echo "Invalid choice. Exiting..."
        exit 1
        ;;
esac

echo "Test completed."
