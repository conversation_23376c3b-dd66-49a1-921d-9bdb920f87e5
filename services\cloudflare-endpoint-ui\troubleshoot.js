const express = require("express");
const axios = require("axios");
const { exec } = require("child_process");
const util = require("util");
const dns = require("dns");
const bodyParser = require("body-parser");
require('dotenv').config();


const execPromise = util.promisify(exec);
const dnsResolve = util.promisify(dns.resolve);

const app = express();
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());

// Environment variables should be used for sensitive information
const CLOUDFLARE_API_TOKEN = process.env.CLOUDFLARE_API_TOKEN;
const ACCOUNT_ID = process.env.ACCOUNT_ID;
const TUNNEL_ID = process.env.TUNNEL_ID;
const ZONE_ID = process.env.ZONE_ID;

// Cloudflare API client
const cloudflareAPI = axios.create({
  baseURL: "https://api.cloudflare.com/client/v4",
  headers: {
    Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
    "Content-Type": "application/json"
  }
});

// Check if a local service is running
async function checkLocalService(url) {
  try {
    // Extract host and port from URL
    const urlObj = new URL(url);
    const host = urlObj.hostname;
    const port = urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80);
    
    // Use netstat to check if the port is listening
    const { stdout } = await execPromise(`netstat -tuln | grep ${port}`);
    return {
      success: stdout.includes(`:${port}`),
      message: stdout.includes(`:${port}`) 
        ? `Service is running on port ${port}` 
        : `No service detected on port ${port}`
    };
  } catch (error) {
    return {
      success: false,
      message: `Error checking local service: ${error.message}`
    };
  }
}

// Check if cloudflared is running
async function checkCloudflaredStatus() {
  try {
    const { stdout } = await execPromise("ps aux | grep cloudflared | grep -v grep");
    return {
      success: stdout.includes("cloudflared"),
      message: stdout.includes("cloudflared") 
        ? "Cloudflared is running" 
        : "Cloudflared is not running"
    };
  } catch (error) {
    return {
      success: false,
      message: `Error checking cloudflared: ${error.message}`
    };
  }
}

// Check DNS resolution
async function checkDNS(hostname) {
  try {
    const records = await dnsResolve(hostname, 'CNAME');
    return {
      success: true,
      message: `DNS resolves to: ${records.join(', ')}`,
      records
    };
  } catch (error) {
    return {
      success: false,
      message: `DNS resolution failed: ${error.message}`
    };
  }
}

// Check tunnel status
async function checkTunnelStatus() {
  try {
    const { data } = await cloudflareAPI.get(
      `/accounts/${ACCOUNT_ID}/cfd_tunnel/${TUNNEL_ID}`
    );
    
    if (!data.success) {
      return {
        success: false,
        message: `Failed to fetch tunnel status: ${JSON.stringify(data.errors)}`
      };
    }
    
    return {
      success: data.result.status === "active",
      message: `Tunnel status: ${data.result.status}`,
      details: data.result
    };
  } catch (error) {
    return {
      success: false,
      message: `Error checking tunnel status: ${error.message}`
    };
  }
}

// Check tunnel connections
async function checkTunnelConnections() {
  try {
    const { data } = await cloudflareAPI.get(
      `/accounts/${ACCOUNT_ID}/cfd_tunnel/${TUNNEL_ID}/connections`
    );
    
    if (!data.success) {
      return {
        success: false,
        message: `Failed to fetch tunnel connections: ${JSON.stringify(data.errors)}`
      };
    }
    
    const activeConnections = data.result.filter(conn => conn.connected_at && !conn.disconnected_at);
    
    return {
      success: activeConnections.length > 0,
      message: activeConnections.length > 0 
        ? `${activeConnections.length} active connections` 
        : "No active connections",
      connections: data.result
    };
  } catch (error) {
    return {
      success: false,
      message: `Error checking tunnel connections: ${error.message}`
    };
  }
}

// Main troubleshooting function
async function troubleshootEndpoint(hostname, localService) {
  const results = {
    dns: await checkDNS(hostname),
    tunnel: await checkTunnelStatus(),
    connections: await checkTunnelConnections(),
    localService: await checkLocalService(localService),
    cloudflared: await checkCloudflaredStatus()
  };
  
  // Determine overall status
  let overallStatus = "Unknown";
  let recommendations = [];
  
  if (!results.dns.success) {
    overallStatus = "DNS Issue";
    recommendations.push("DNS is not resolving correctly. Check your DNS configuration in Cloudflare.");
  } else if (!results.tunnel.success) {
    overallStatus = "Tunnel Issue";
    recommendations.push("The tunnel is not active. Make sure your tunnel is created and running.");
  } else if (!results.connections.success) {
    overallStatus = "Connection Issue";
    recommendations.push("No active connections to your tunnel. Make sure cloudflared is running.");
  } else if (!results.localService.success) {
    overallStatus = "Local Service Issue";
    recommendations.push(`The local service at ${localService} doesn't appear to be running.`);
  } else if (!results.cloudflared.success) {
    overallStatus = "Cloudflared Issue";
    recommendations.push("The cloudflared daemon doesn't appear to be running. Start it with 'cloudflared tunnel run <tunnel-name>'");
  } else {
    overallStatus = "All checks passed";
    recommendations.push("All systems appear to be working. If you're still having issues, check for firewall rules or try accessing from a different network.");
  }
  
  return {
    overallStatus,
    recommendations,
    results
  };
}

app.get("/", (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Cloudflare Tunnel Troubleshooter</title>
      <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        form { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        input { width: 100%; padding: 8px; margin: 5px 0 15px 0; }
        button { background: #0070f3; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0051a2; }
        .info { background: #e6f7ff; border-left: 4px solid #1890ff; padding: 10px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <h1>Cloudflare Tunnel Troubleshooter</h1>
      <div class="info">
        <p>This tool will diagnose issues with your Cloudflare Tunnel setup.</p>
      </div>
      <form action="/troubleshoot" method="POST">
        <label for="hostname">Public Hostname:</label>
        <input type="text" id="hostname" name="hostname" placeholder="example.yourdomain.com" required />
        
        <label for="localService">Local Service URL:</label>
        <input type="text" id="localService" name="localService" placeholder="http://localhost:8080" required />
        
        <button type="submit">Diagnose</button>
      </form>
    </body>
    </html>
  `);
});

app.post("/troubleshoot", async (req, res) => {
  const { hostname, localService } = req.body;
  
  if (!hostname || !localService) {
    return res.status(400).send(`
      <h2>Error: All fields are required.</h2>
      <a href="/">Go back</a>
    `);
  }
  
  try {
    const diagnosis = await troubleshootEndpoint(hostname, localService);
    
    res.send(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Diagnosis Results</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          .success { color: green; }
          .error { color: red; }
          .warning { color: orange; }
          .card { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 15px; }
          .recommendations { background: #e6f7ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
          pre { background: #f0f0f0; padding: 10px; border-radius: 5px; overflow-x: auto; }
          a { color: #0070f3; text-decoration: none; }
          a:hover { text-decoration: underline; }
        </style>
      </head>
      <body>
        <h1>Diagnosis Results for ${hostname}</h1>
        
        <h2>Overall Status: <span class="${diagnosis.overallStatus === 'All checks passed' ? 'success' : 'error'}">${diagnosis.overallStatus}</span></h2>
        
        <div class="recommendations">
          <h3>Recommendations:</h3>
          <ul>
            ${diagnosis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
          </ul>
        </div>
        
        <h3>Detailed Results:</h3>
        
        <div class="card">
          <h4>DNS Check: <span class="${diagnosis.results.dns.success ? 'success' : 'error'}">${diagnosis.results.dns.success ? 'Passed' : 'Failed'}</span></h4>
          <p>${diagnosis.results.dns.message}</p>
          ${diagnosis.results.dns.records ? `<pre>${JSON.stringify(diagnosis.results.dns.records, null, 2)}</pre>` : ''}
        </div>
        
        <div class="card">
          <h4>Tunnel Status: <span class="${diagnosis.results.tunnel.success ? 'success' : 'error'}">${diagnosis.results.tunnel.success ? 'Active' : 'Inactive'}</span></h4>
          <p>${diagnosis.results.tunnel.message}</p>
          ${diagnosis.results.tunnel.details ? `<pre>${JSON.stringify(diagnosis.results.tunnel.details, null, 2)}</pre>` : ''}
        </div>
        
        <div class="card">
          <h4>Tunnel Connections: <span class="${diagnosis.results.connections.success ? 'success' : 'error'}">${diagnosis.results.connections.success ? 'Connected' : 'Not Connected'}</span></h4>
          <p>${diagnosis.results.connections.message}</p>
          ${diagnosis.results.connections.connections ? `<pre>${JSON.stringify(diagnosis.results.connections.connections, null, 2)}</pre>` : ''}
        </div>
        
        <div class="card">
          <h4>Local Service: <span class="${diagnosis.results.localService.success ? 'success' : 'error'}">${diagnosis.results.localService.success ? 'Running' : 'Not Running'}</span></h4>
          <p>${diagnosis.results.localService.message}</p>
        </div>
        
        <div class="card">
          <h4>Cloudflared Status: <span class="${diagnosis.results.cloudflared.success ? 'success' : 'error'}">${diagnosis.results.cloudflared.success ? 'Running' : 'Not Running'}</span></h4>
          <p>${diagnosis.results.cloudflared.message}</p>
        </div>
        
        <h3>Common Solutions:</h3>
        
        <div class="card">
          <h4>If cloudflared is not running:</h4>
          <p>Start your tunnel with:</p>
          <pre>cloudflared tunnel run ${TUNNEL_ID}</pre>
          <p>Or if you're using a named tunnel:</p>
          <pre>cloudflared tunnel run your-tunnel-name</pre>
        </div>
        
        <div class="card">
          <h4>If DNS is not resolving:</h4>
          <p>Make sure your DNS record is correctly set up in Cloudflare:</p>
          <ul>
            <li>Type: CNAME</li>
            <li>Name: ${hostname.split('.')[0]}</li>
            <li>Target: ${TUNNEL_ID}.cfargotunnel.com</li>
            <li>Proxy status: Proxied</li>
          </ul>
        </div>
        
        <div class="card">
          <h4>If local service is not running:</h4>
          <p>Make sure your application is running on the correct port:</p>
          <pre>${localService}</pre>
        </div>
        
        <a href="/">← Run another diagnosis</a>
      </body>
      </html>
    `);
  } catch (error) {
    console.error("Error during troubleshooting:", error);
    res.status(500).send(`
      <h2>Error during troubleshooting</h2>
      <p>${error.message}</p>
      <a href="/">← Try again</a>
    `);
  }
});

const PORT = process.env.PORT || 1901;
app.listen(PORT, () => {
  console.log(`Troubleshooter running at http://localhost:${PORT}`);
});
