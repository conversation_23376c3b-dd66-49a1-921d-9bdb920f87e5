{"db_name": "PostgreSQL", "query": "\n            SELECT email_otp, email_otp_expires_at\n            FROM users\n            WHERE email = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "email_otp", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "email_otp_expires_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Text"]}, "nullable": [true, true]}, "hash": "06f9c82398b1aa2c82cbd9bf4c6e9512449ddb85e7bbf8957a88e9fc57c8c983"}