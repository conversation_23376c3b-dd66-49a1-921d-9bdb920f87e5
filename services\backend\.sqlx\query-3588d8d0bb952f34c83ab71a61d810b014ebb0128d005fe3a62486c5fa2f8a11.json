{"db_name": "PostgreSQL", "query": "\n            SELECT\n                om.id as \"member_id!\", om.organization_id, om.user_id as \"member_user_id!\", om.role as \"role!: OrganizationRole\",\n                om.invited_by, om.joined_at, om.settings, om.created_at as \"member_created_at!\", om.updated_at as \"member_updated_at!\",\n                u.id as \"user_id!\", u.name, u.email, u.role as \"user_role!: UserRole\", u.email_otp,\n                u.email_otp_expires_at, u.profile_data, u.default_organization_id, u.created_at as \"user_created_at!\", u.updated_at as \"user_updated_at!\"\n            FROM organization_members om\n            JOIN users u ON om.user_id = u.id\n            WHERE om.organization_id = $1\n            ORDER BY om.created_at DESC\n            LIMIT $2 OFFSET $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "member_id!", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "member_user_id!", "type_info": "<PERSON><PERSON>"}, {"ordinal": 3, "name": "role!: OrganizationRole", "type_info": {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}}, {"ordinal": 4, "name": "invited_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 5, "name": "joined_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "settings", "type_info": "Jsonb"}, {"ordinal": 7, "name": "member_created_at!", "type_info": "Timestamptz"}, {"ordinal": 8, "name": "member_updated_at!", "type_info": "Timestamptz"}, {"ordinal": 9, "name": "user_id!", "type_info": "<PERSON><PERSON>"}, {"ordinal": 10, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 11, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 12, "name": "user_role!: UserRole", "type_info": {"Custom": {"name": "user_role", "kind": {"Enum": ["user", "admin"]}}}}, {"ordinal": 13, "name": "email_otp", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 14, "name": "email_otp_expires_at", "type_info": "Timestamptz"}, {"ordinal": 15, "name": "profile_data", "type_info": "Jsonb"}, {"ordinal": 16, "name": "default_organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 17, "name": "user_created_at!", "type_info": "Timestamptz"}, {"ordinal": 18, "name": "user_updated_at!", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "Int8", "Int8"]}, "nullable": [false, false, false, false, true, false, true, false, false, false, false, false, false, true, true, true, true, false, false]}, "hash": "3588d8d0bb952f34c83ab71a61d810b014ebb0128d005fe3a62486c5fa2f8a11"}