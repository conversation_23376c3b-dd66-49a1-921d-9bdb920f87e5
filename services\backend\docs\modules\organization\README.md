# Organization Module

The Organization module provides functionality for creating and managing organizations, inviting team members with different roles, and collaborating effectively.

## Features

- Create and manage organizations
- Invite team members with different roles
- Role-based access control
- Organization settings management
- Default organization selection

## User Journey

### Organization Creation and Management

1. After authentication, if the user doesn't have any organizations, they are prompted to create one
2. User enters the organization name and optional details (description, domain)
3. System creates the organization and automatically assigns the user as an OrgAdmin
4. The newly created organization becomes the user's default organization

### Team Member Management

1. Organization admins and editors can invite new members by email
2. When inviting, the admin/editor specifies the role for the new member
3. System sends an invitation email to the specified address
4. The invitation includes a link to accept the invitation
5. Invited user receives an email with an invitation link
6. After authentication, the user can accept the invitation
7. Upon acceptance, the user becomes a member of the organization with the assigned role

## Role-Based Access Control

### Organization Roles

- **OrgAdmin**: Full access to the organization, can manage members and settings
- **OrgEditor**: Can edit organization content and invite members
- **OrgAgent**: Can perform agent-specific actions
- **OrgUser**: Standard user with basic access
- **OrgViewer**: Read-only access to the organization

### Role Permissions

| Action | OrgAdmin | OrgEditor | OrgAgent | OrgUser | OrgViewer |
|--------|----------|-----------|----------|---------|-----------|
| View Organization | ✅ | ✅ | ✅ | ✅ | ✅ |
| Edit Organization | ✅ | ✅ | ❌ | ❌ | ❌ |
| Delete Organization | ✅ | ❌ | ❌ | ❌ | ❌ |
| Invite Members | ✅ | ✅ | ❌ | ❌ | ❌ |
| Manage Members | ✅ | ❌ | ❌ | ❌ | ❌ |
| Create Content | ✅ | ✅ | ✅ | ✅ | ❌ |
| Edit Content | ✅ | ✅ | ✅ | ✅ | ❌ |
| View Content | ✅ | ✅ | ✅ | ✅ | ✅ |

## Database Schema

### Organizations Table

```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    domain VARCHAR(255),
    settings JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### Organization Members Table

```sql
CREATE TABLE organization_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role organization_role NOT NULL DEFAULT 'orguser',
    invited_by UUID REFERENCES users(id),
    joined_at TIMESTAMPTZ,
    settings JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(organization_id, user_id)
);
```

### Organization Invitations Table

```sql
CREATE TABLE organization_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    role organization_role NOT NULL DEFAULT 'orguser',
    invited_by UUID NOT NULL REFERENCES users(id),
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(organization_id, email)
);
```

### User Default Organization Table

```sql
CREATE TABLE user_default_organizations (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

## API Endpoints

For detailed information about the Organization API endpoints, see the [Organization API Reference](../../api/organization.md).

## Implementation Details

The Organization module is implemented in the following files:

- `src/handler/organization.rs` - HTTP request handlers
- `src/models/organization.rs` - Database models
- `src/dtos.rs` - Data Transfer Objects
- `src/db/organization.rs` - Database access functions

## Best Practices

1. **Role Assignment**: Assign the minimum necessary role to each team member
2. **Regular Audits**: Periodically review organization members and their roles
3. **Secure Invitations**: Ensure invitation links are only shared with intended recipients
4. **Default Organization**: Set your most frequently used organization as the default

## Troubleshooting

### Common Issues

1. **Invitation Not Received**
   - Check spam/junk folder
   - Verify the email address is correct
   - Ask the admin to resend the invitation

2. **Cannot Accept Invitation**
   - Ensure you're logged in with the email address the invitation was sent to
   - Check if the invitation has expired (valid for 7 days)
   - Contact the organization admin for a new invitation

3. **Permission Denied**
   - Verify your role in the organization
   - Contact the organization admin if you need additional permissions
