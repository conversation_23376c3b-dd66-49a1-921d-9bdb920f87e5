#[cfg(test)]
mod tests {
    use chrono::{Duration, Utc};
    use jsonwebtoken::{decode, encode, Algorithm, Decoding<PERSON><PERSON>, <PERSON>co<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Valida<PERSON>};
    use serde::{Deserialize, Serialize};
    use std::env;

    #[derive(Debug, Serialize, Deserialize)]
    struct TokenClaims {
        pub sub: String,
        pub iat: usize,
        pub exp: usize,
    }

    #[test]
    fn test_jwt_validation() {
        // Load environment variables
        dotenv::dotenv().ok();
        
        // Get JWT secret from environment
        let jwt_secret = env::var("JWT_SECRET_KEY").expect("JWT_SECRET_KEY must be set");
        println!("JWT Secret length: {}", jwt_secret.len());
        println!("JWT Secret first 5 chars: {}", &jwt_secret[0..5]);
        
        // Create a test token
        let user_id = "test-user-id";
        let now = Utc::now();
        let iat = now.timestamp() as usize;
        let exp = (now + Duration::minutes(60)).timestamp() as usize;
        
        let claims = TokenClaims {
            sub: user_id.to_string(),
            iat,
            exp,
        };
        
        // Encode token
        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(jwt_secret.as_bytes())
        ).expect("Failed to encode token");
        
        println!("Generated token: {}", token);
        
        // Decode token
        let decoded = decode::<TokenClaims>(
            &token,
            &DecodingKey::from_secret(jwt_secret.as_bytes()),
            &Validation::new(Algorithm::HS256),
        ).expect("Failed to decode token");
        
        println!("Decoded token sub: {}", decoded.claims.sub);
        
        // Verify the decoded token matches the original user_id
        assert_eq!(decoded.claims.sub, user_id);
        
        // Try to decode with a wrong secret
        let wrong_secret = "wrong-secret";
        let decode_result = decode::<TokenClaims>(
            &token,
            &DecodingKey::from_secret(wrong_secret.as_bytes()),
            &Validation::new(Algorithm::HS256),
        );
        
        // This should fail
        assert!(decode_result.is_err());
        println!("Decode with wrong secret failed as expected: {:?}", decode_result.err());
    }
}
