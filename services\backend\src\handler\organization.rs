use std::sync::Arc;

use axum::{
    extract::{Path, Query},
    http::StatusCode,
    middleware,
    response::IntoResponse,
    routing::{get, post, put, delete},
    Extension, Json, Router,
};
use chrono::{Duration, Utc};
use uuid::Uuid;
use validator::Validate;

use crate::{
    db::{OrganizationExt, UserExt},
    dtos::{
        AcceptInvitationDto, CreateOrganizationDto, FilterOrganizationDto, FilterOrganizationInvitationDto,
        FilterOrganizationMemberDto, InviteMemberDto, OrganizationData, OrganizationListResponseDto,
        OrganizationMemberData, OrganizationMemberListResponseDto, OrganizationMemberResponseDto,
        OrganizationResponseDto, RequestQueryDto, Response, UpdateMemberRoleDto,
    },
    error::{ErrorMessage, HttpError},
    mail::mails::send_organization_invitation_email,
    middleware::{auth, role_check, JWTAuthMiddeware},
    models::{OrganizationRole, UserRole},
    utils::token,
    AppState,
};

pub fn organization_handler(app_state: Arc<AppState>) -> Router {
    Router::new()
        .route(
            "/",
            post(create_organization)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                }))
                .get(get_user_organizations)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                })),
        )
        .route(
            "/:id",
            get(get_organization)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                }))
                .put(update_organization)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                }))
                .delete(delete_organization)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                })),
        )
        .route(
            "/:id/members",
            get(get_organization_members)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                }))
                .post(invite_member)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                })),
        )
        .route(
            "/:id/members/:member_id",
            put(update_member_role)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                }))
                .delete(remove_member)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                })),
        )
        .route(
            "/invitations/accept",
            post(accept_invitation)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                })),
        )
        .route(
            "/invitations",
            get(get_user_invitations)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                })),
        )
        .route(
            "/:id/invitations",
            get(get_organization_invitations)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                })),
        )
        .route(
            "/default/:id",
            put(set_default_organization)
                .layer(middleware::from_fn(auth))
                .layer(middleware::from_fn_with_state(app_state.clone(), |state, req, next| {
                    role_check(state, req, next, vec![UserRole::Admin, UserRole::User])
                })),
        )
}

async fn create_organization(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Json(body): Json<CreateOrganizationDto>,
) -> Result<impl IntoResponse, HttpError> {
    body.validate()
        .map_err(|e| HttpError::bad_request(e.to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    let organization = app_state
        .db_client
        .create_organization(
            body.name,
            body.description,
            body.domain,
            body.settings,
            user_id,
        )
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let filtered_organization = FilterOrganizationDto::filter_organization(&organization);

    let response = OrganizationResponseDto {
        status: "success".to_string(),
        data: OrganizationData {
            organization: filtered_organization,
        },
    };

    Ok((StatusCode::CREATED, Json(response)))
}

async fn get_user_organizations(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Query(query_params): Query<RequestQueryDto>,
) -> Result<impl IntoResponse, HttpError> {
    query_params
        .validate()
        .map_err(|e| HttpError::bad_request(e.to_string()))?;

    let page = query_params.page.unwrap_or(1);
    let limit = query_params.limit.unwrap_or(10);

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    let organizations = app_state
        .db_client
        .get_user_organizations(user_id, page as u32, limit)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let count = app_state
        .db_client
        .get_organization_count_for_user(user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let filtered_organizations = FilterOrganizationDto::filter_organizations(&organizations);

    let response = OrganizationListResponseDto {
        status: "success".to_string(),
        organizations: filtered_organizations,
        results: count,
    };

    Ok(Json(response))
}

async fn get_organization(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Path(id): Path<String>,
) -> Result<impl IntoResponse, HttpError> {
    let organization_id = Uuid::parse_str(&id)
        .map_err(|_| HttpError::bad_request("Invalid organization ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Check if the user is a member of the organization
    let member = app_state
        .db_client
        .get_organization_member(organization_id, user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if member.is_none() {
        return Err(HttpError::new(
            ErrorMessage::PermissionDenied.to_string(),
            StatusCode::FORBIDDEN,
        ));
    }

    let organization = app_state
        .db_client
        .get_organization(organization_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| HttpError::not_found("Organization not found".to_string()))?;

    let filtered_organization = FilterOrganizationDto::filter_organization(&organization);

    let response = OrganizationResponseDto {
        status: "success".to_string(),
        data: OrganizationData {
            organization: filtered_organization,
        },
    };

    Ok(Json(response))
}

async fn update_organization(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Path(id): Path<String>,
    Json(body): Json<CreateOrganizationDto>,
) -> Result<impl IntoResponse, HttpError> {
    body.validate()
        .map_err(|e| HttpError::bad_request(e.to_string()))?;

    let organization_id = Uuid::parse_str(&id)
        .map_err(|_| HttpError::bad_request("Invalid organization ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Check if the user is an admin of the organization
    let member = app_state
        .db_client
        .get_organization_member(organization_id, user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| {
            HttpError::new(
                ErrorMessage::PermissionDenied.to_string(),
                StatusCode::FORBIDDEN,
            )
        })?;

    if member.role != OrganizationRole::OrgAdmin {
        return Err(HttpError::new(
            ErrorMessage::PermissionDenied.to_string(),
            StatusCode::FORBIDDEN,
        ));
    }

    let organization = app_state
        .db_client
        .update_organization(
            organization_id,
            body.name,
            body.description,
            body.domain,
            body.settings,
        )
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let filtered_organization = FilterOrganizationDto::filter_organization(&organization);

    let response = OrganizationResponseDto {
        status: "success".to_string(),
        data: OrganizationData {
            organization: filtered_organization,
        },
    };

    Ok(Json(response))
}

async fn delete_organization(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Path(id): Path<String>,
) -> Result<impl IntoResponse, HttpError> {
    let organization_id = Uuid::parse_str(&id)
        .map_err(|_| HttpError::bad_request("Invalid organization ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Check if the user is an admin of the organization
    let member = app_state
        .db_client
        .get_organization_member(organization_id, user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| {
            HttpError::new(
                ErrorMessage::PermissionDenied.to_string(),
                StatusCode::FORBIDDEN,
            )
        })?;

    if member.role != OrganizationRole::OrgAdmin {
        return Err(HttpError::new(
            ErrorMessage::PermissionDenied.to_string(),
            StatusCode::FORBIDDEN,
        ));
    }

    app_state
        .db_client
        .delete_organization(organization_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let response = Response {
        status: "success",
        message: "Organization deleted successfully".to_string(),
    };

    Ok(Json(response))
}

async fn get_organization_members(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Path(id): Path<String>,
    Query(query_params): Query<RequestQueryDto>,
) -> Result<impl IntoResponse, HttpError> {
    query_params
        .validate()
        .map_err(|e| HttpError::bad_request(e.to_string()))?;

    let organization_id = Uuid::parse_str(&id)
        .map_err(|_| HttpError::bad_request("Invalid organization ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Check if the user is a member of the organization
    let member = app_state
        .db_client
        .get_organization_member(organization_id, user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if member.is_none() {
        return Err(HttpError::new(
            ErrorMessage::PermissionDenied.to_string(),
            StatusCode::FORBIDDEN,
        ));
    }

    let page = query_params.page.unwrap_or(1);
    let limit = query_params.limit.unwrap_or(10);

    let members = app_state
        .db_client
        .get_organization_members(organization_id, page as u32, limit)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let count = app_state
        .db_client
        .get_organization_member_count(organization_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let filtered_members = members
        .into_iter()
        .map(|(member, user)| FilterOrganizationMemberDto {
            id: member.id.to_string(),
            organization_id: member.organization_id.to_string(),
            user_id: member.user_id.to_string(),
            role: member.role.to_str().to_string(),
            invited_by: member.invited_by.map(|id| id.to_string()),
            joined_at: member.joined_at,
            settings: member.settings,
            created_at: member.created_at,
            updated_at: member.updated_at,
            user_name: user.name,
            user_email: user.email,
        })
        .collect();

    let response = OrganizationMemberListResponseDto {
        status: "success".to_string(),
        members: filtered_members,
        results: count,
    };

    Ok(Json(response))
}

async fn invite_member(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Path(id): Path<String>,
    Json(body): Json<InviteMemberDto>,
) -> Result<impl IntoResponse, HttpError> {
    body.validate()
        .map_err(|e| HttpError::bad_request(e.to_string()))?;

    let organization_id = Uuid::parse_str(&id)
        .map_err(|_| HttpError::bad_request("Invalid organization ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Check if the user is an admin or editor of the organization
    let member = app_state
        .db_client
        .get_organization_member(organization_id, user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| {
            HttpError::new(
                ErrorMessage::PermissionDenied.to_string(),
                StatusCode::FORBIDDEN,
            )
        })?;

    if member.role != OrganizationRole::OrgAdmin && member.role != OrganizationRole::OrgEditor {
        return Err(HttpError::new(
            ErrorMessage::PermissionDenied.to_string(),
            StatusCode::FORBIDDEN,
        ));
    }

    // Check if the organization exists
    let organization = app_state
        .db_client
        .get_organization(organization_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| HttpError::not_found("Organization not found".to_string()))?;

    // Check if the user is already a member
    let existing_user = app_state
        .db_client
        .get_user(None, None, Some(&body.email))
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if let Some(existing_user) = existing_user {
        let existing_member = app_state
            .db_client
            .get_organization_member(organization_id, existing_user.id)
            .await
            .map_err(|e| HttpError::server_error(e.to_string()))?;

        if existing_member.is_some() {
            return Err(HttpError::bad_request(
                "User is already a member of this organization".to_string(),
            ));
        }
    }

    // Check if there's already a pending invitation for this email
    let existing_invitation = app_state
        .db_client
        .get_invitation_by_email(organization_id, &body.email)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if existing_invitation.is_some() {
        return Err(HttpError::bad_request(
            "An invitation has already been sent to this email".to_string(),
        ));
    }

    // Parse the role
    let role = OrganizationRole::from_str(&body.role)
        .map_err(|e| HttpError::bad_request(e))?;

    // Generate a unique token for the invitation
    let random_id = uuid::Uuid::new_v4().to_string();
    let token = token::create_token(
        &random_id,
        app_state.env.jwt_secret.as_bytes(),
        60 * 24 * 7 // 7 days in minutes
    ).map_err(|e| HttpError::server_error(e.to_string()))?;
    let expires_at = Utc::now() + Duration::days(7);

    // Create the invitation
    let invitation = app_state
        .db_client
        .create_organization_invitation(
            organization_id,
            &body.email,
            role,
            user_id,
            token.clone(),
            expires_at,
        )
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Get the inviter's name
    let inviter_name = &auth.user.name;

    // Generate the invitation link
    let invitation_link = format!(
        "{}/api/organizations/invitations/accept?token={}",
        app_state.env.frontend_url,
        token
    );

    // Send the invitation email
    let _ = send_organization_invitation_email(
        &body.email,
        inviter_name,
        &organization.name,
        role.to_str(),
        &invitation_link,
    )
    .await;

    let response = Response {
        status: "success",
        message: format!("Invitation sent to {}", body.email),
    };

    Ok(Json(response))
}

async fn update_member_role(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Path((org_id, member_id)): Path<(String, String)>,
    Json(body): Json<UpdateMemberRoleDto>,
) -> Result<impl IntoResponse, HttpError> {
    let organization_id = Uuid::parse_str(&org_id)
        .map_err(|_| HttpError::bad_request("Invalid organization ID".to_string()))?;

    let member_id = Uuid::parse_str(&member_id)
        .map_err(|_| HttpError::bad_request("Invalid member ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Check if the user is an admin of the organization
    let user_member = app_state
        .db_client
        .get_organization_member(organization_id, user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| {
            HttpError::new(
                ErrorMessage::PermissionDenied.to_string(),
                StatusCode::FORBIDDEN,
            )
        })?;

    if user_member.role != OrganizationRole::OrgAdmin {
        return Err(HttpError::new(
            ErrorMessage::PermissionDenied.to_string(),
            StatusCode::FORBIDDEN,
        ));
    }

    // Get the member to update
    let target_member = app_state
        .db_client
        .get_organization_member_by_id(member_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| HttpError::not_found("Member not found".to_string()))?;

    // Ensure the member belongs to the specified organization
    if target_member.organization_id != organization_id {
        return Err(HttpError::bad_request(
            "Member does not belong to this organization".to_string(),
        ));
    }

    // Parse the role
    let role = OrganizationRole::from_str(&body.role)
        .map_err(|e| HttpError::bad_request(e))?;

    // Update the member's role
    let updated_member = app_state
        .db_client
        .update_organization_member_role(member_id, role)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Get the user associated with the member
    let user = app_state
        .db_client
        .get_user(Some(updated_member.user_id), None, None)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| HttpError::not_found("User not found".to_string()))?;

    let filtered_member = FilterOrganizationMemberDto {
        id: updated_member.id.to_string(),
        organization_id: updated_member.organization_id.to_string(),
        user_id: updated_member.user_id.to_string(),
        role: updated_member.role.to_str().to_string(),
        invited_by: updated_member.invited_by.map(|id| id.to_string()),
        joined_at: updated_member.joined_at,
        settings: updated_member.settings,
        created_at: updated_member.created_at,
        updated_at: updated_member.updated_at,
        user_name: user.name,
        user_email: user.email,
    };

    let response = OrganizationMemberResponseDto {
        status: "success".to_string(),
        data: OrganizationMemberData {
            member: filtered_member,
        },
    };

    Ok(Json(response))
}

async fn remove_member(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Path((org_id, member_id)): Path<(String, String)>,
) -> Result<impl IntoResponse, HttpError> {
    let organization_id = Uuid::parse_str(&org_id)
        .map_err(|_| HttpError::bad_request("Invalid organization ID".to_string()))?;

    let member_id = Uuid::parse_str(&member_id)
        .map_err(|_| HttpError::bad_request("Invalid member ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Check if the user is an admin of the organization
    let user_member = app_state
        .db_client
        .get_organization_member(organization_id, user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| {
            HttpError::new(
                ErrorMessage::PermissionDenied.to_string(),
                StatusCode::FORBIDDEN,
            )
        })?;

    if user_member.role != OrganizationRole::OrgAdmin {
        return Err(HttpError::new(
            ErrorMessage::PermissionDenied.to_string(),
            StatusCode::FORBIDDEN,
        ));
    }

    // Get the member to remove
    let target_member = app_state
        .db_client
        .get_organization_member_by_id(member_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| HttpError::not_found("Member not found".to_string()))?;

    // Ensure the member belongs to the specified organization
    if target_member.organization_id != organization_id {
        return Err(HttpError::bad_request(
            "Member does not belong to this organization".to_string(),
        ));
    }

    // Prevent removing yourself
    if target_member.user_id == user_id {
        return Err(HttpError::bad_request(
            "You cannot remove yourself from the organization".to_string(),
        ));
    }

    // Remove the member
    app_state
        .db_client
        .remove_organization_member(organization_id, target_member.user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let response = Response {
        status: "success",
        message: "Member removed successfully".to_string(),
    };

    Ok(Json(response))
}

async fn accept_invitation(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Json(body): Json<AcceptInvitationDto>,
) -> Result<impl IntoResponse, HttpError> {
    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Get the invitation
    let invitation_data = app_state
        .db_client
        .get_invitation_by_token(&body.token)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| HttpError::not_found("Invitation not found or expired".to_string()))?;

    let (invitation, organization, _) = invitation_data;

    // Check if the invitation is for the authenticated user
    if invitation.email != auth.user.email {
        return Err(HttpError::bad_request(
            "This invitation is not for your email address".to_string(),
        ));
    }

    // Check if the user is already a member
    let existing_member = app_state
        .db_client
        .get_organization_member(invitation.organization_id, user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if existing_member.is_some() {
        return Err(HttpError::bad_request(
            "You are already a member of this organization".to_string(),
        ));
    }

    // Add the user to the organization
    let member = app_state
        .db_client
        .add_organization_member(
            invitation.organization_id,
            user_id,
            invitation.role,
            Some(invitation.invited_by),
        )
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Update the invitation status
    app_state
        .db_client
        .update_invitation_status(invitation.id, "accepted")
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    // Set as default organization if the user doesn't have one yet
    if auth.user.default_organization_id.is_none() {
        app_state
            .db_client
            .set_default_organization(user_id, invitation.organization_id)
            .await
            .map_err(|e| HttpError::server_error(e.to_string()))?;
    }

    let response = Response {
        status: "success",
        message: format!("You have joined {}", organization.name),
    };

    Ok(Json(response))
}

async fn get_user_invitations(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
) -> Result<impl IntoResponse, HttpError> {
    let invitations = app_state
        .db_client
        .get_user_invitations(&auth.user.email)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let filtered_invitations = invitations
        .into_iter()
        .map(|(invitation, organization, inviter)| FilterOrganizationInvitationDto {
            id: invitation.id.to_string(),
            organization_id: invitation.organization_id.to_string(),
            email: invitation.email,
            role: invitation.role.to_str().to_string(),
            invited_by: invitation.invited_by.to_string(),
            token: invitation.token,
            expires_at: invitation.expires_at,
            status: invitation.status,
            created_at: invitation.created_at,
            updated_at: invitation.updated_at,
            organization_name: organization.name,
            invited_by_name: inviter.name,
        })
        .collect::<Vec<_>>();

    let response = Json(filtered_invitations);

    Ok(response)
}

async fn get_organization_invitations(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Path(id): Path<String>,
    Query(query_params): Query<RequestQueryDto>,
) -> Result<impl IntoResponse, HttpError> {
    query_params
        .validate()
        .map_err(|e| HttpError::bad_request(e.to_string()))?;

    let organization_id = Uuid::parse_str(&id)
        .map_err(|_| HttpError::bad_request("Invalid organization ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Check if the user is an admin or editor of the organization
    let member = app_state
        .db_client
        .get_organization_member(organization_id, user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?
        .ok_or_else(|| {
            HttpError::new(
                ErrorMessage::PermissionDenied.to_string(),
                StatusCode::FORBIDDEN,
            )
        })?;

    if member.role != OrganizationRole::OrgAdmin && member.role != OrganizationRole::OrgEditor {
        return Err(HttpError::new(
            ErrorMessage::PermissionDenied.to_string(),
            StatusCode::FORBIDDEN,
        ));
    }

    let page = query_params.page.unwrap_or(1);
    let limit = query_params.limit.unwrap_or(10);

    let invitations = app_state
        .db_client
        .get_organization_invitations(organization_id, page as u32, limit)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let filtered_invitations = invitations
        .into_iter()
        .map(|(invitation, inviter)| FilterOrganizationInvitationDto {
            id: invitation.id.to_string(),
            organization_id: invitation.organization_id.to_string(),
            email: invitation.email,
            role: invitation.role.to_str().to_string(),
            invited_by: invitation.invited_by.to_string(),
            token: invitation.token,
            expires_at: invitation.expires_at,
            status: invitation.status,
            created_at: invitation.created_at,
            updated_at: invitation.updated_at,
            organization_name: "".to_string(), // Not needed in this context
            invited_by_name: inviter.name,
        })
        .collect::<Vec<_>>();

    let response = Json(filtered_invitations);

    Ok(response)
}

async fn set_default_organization(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Path(id): Path<String>,
) -> Result<impl IntoResponse, HttpError> {
    let organization_id = Uuid::parse_str(&id)
        .map_err(|_| HttpError::bad_request("Invalid organization ID".to_string()))?;

    let user_id = Uuid::parse_str(&auth.user.id.to_string())
        .map_err(|_| HttpError::bad_request("Invalid user ID".to_string()))?;

    // Check if the user is a member of the organization
    let member = app_state
        .db_client
        .get_organization_member(organization_id, user_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    if member.is_none() {
        return Err(HttpError::new(
            ErrorMessage::PermissionDenied.to_string(),
            StatusCode::FORBIDDEN,
        ));
    }

    // Set as default organization
    let user = app_state
        .db_client
        .set_default_organization(user_id, organization_id)
        .await
        .map_err(|e| HttpError::server_error(e.to_string()))?;

    let response = Response {
        status: "success",
        message: "Default organization updated successfully".to_string(),
    };

    Ok(Json(response))
}