# Organization API

The Organization API provides endpoints for creating and managing organizations, inviting team members, and collaborating effectively.

## Authentication

All Organization API endpoints require authentication. The JWT token can be provided in two ways:

1. As a cookie (set automatically after login)
2. In the Authorization header: `Authorization: Bearer YOUR_JWT_TOKEN`

## Organization Roles

The Organization API uses role-based access control to determine what actions a user can perform. The following roles are available:

- **OrgAdmin**: Full access to the organization, can manage members and settings
- **OrgEditor**: Can edit organization content and invite members
- **OrgAgent**: Can perform agent-specific actions
- **OrgUser**: Standard user with basic access
- **OrgViewer**: Read-only access to the organization

### Role Permissions

| Action | OrgAdmin | OrgEditor | OrgAgent | OrgUser | OrgViewer |
|--------|----------|-----------|----------|---------|-----------|
| View Organization | ✅ | ✅ | ✅ | ✅ | ✅ |
| Edit Organization | ✅ | ✅ | ❌ | ❌ | ❌ |
| Delete Organization | ✅ | ❌ | ❌ | ❌ | ❌ |
| Invite Members | ✅ | ✅ | ❌ | ❌ | ❌ |
| Manage Members | ✅ | ❌ | ❌ | ❌ | ❌ |
| Create Content | ✅ | ✅ | ✅ | ✅ | ❌ |
| Edit Content | ✅ | ✅ | ✅ | ✅ | ❌ |
| View Content | ✅ | ✅ | ✅ | ✅ | ✅ |

## Organization Endpoints

### Create Organization

Creates a new organization. The authenticated user automatically becomes an OrgAdmin of the new organization.

- **URL**: `/organizations`
- **Method**: `POST`
- **Auth Required**: Yes
- **Permissions**: Any authenticated user

#### Request Body

```json
{
  "name": "My Organization",
  "description": "This is my organization",
  "domain": "example.com",
  "settings": {
    "color": "#FF5733",
    "logo": "https://example.com/logo.png"
  }
}
```

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `name` | string | Yes | Name of the organization |
| `description` | string | No | Description of the organization |
| `domain` | string | No | Domain associated with the organization |
| `settings` | object | No | Organization settings (color, logo, etc.) |

#### Success Response

```json
{
  "status": "success",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "My Organization",
    "description": "This is my organization",
    "domain": "example.com",
    "settings": {
      "color": "#FF5733",
      "logo": "https://example.com/logo.png"
    },
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Error Responses

Invalid input:

```json
{
  "status": "error",
  "message": "Name is required"
}
```

Authentication error:

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

### Get User Organizations

Returns all organizations that the authenticated user is a member of.

- **URL**: `/organizations`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions**: Any authenticated user

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | integer | No | 1 | Page number for pagination |
| `limit` | integer | No | 10 | Number of items per page (max: 50) |
| `name` | string | No | - | Filter organizations by name |
| `domain` | string | No | - | Filter organizations by domain |

#### Success Response

```json
{
  "status": "success",
  "organizations": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "My Organization",
      "description": "This is my organization",
      "domain": "example.com",
      "settings": {
        "color": "#FF5733",
        "logo": "https://example.com/logo.png"
      },
      "role": "orgadmin",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    },
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "Another Organization",
      "description": "This is another organization",
      "domain": "another-example.com",
      "settings": {
        "color": "#33FF57",
        "logo": "https://example.com/another-logo.png"
      },
      "role": "orguser",
      "createdAt": "2023-01-02T00:00:00Z",
      "updatedAt": "2023-01-02T00:00:00Z"
    }
  ],
  "results": 2
}
```

#### Error Responses

Authentication error:

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

### Get Organization

Returns details for a specific organization.

- **URL**: `/organizations/:id`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions**: Member of the organization

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string (UUID) | Yes | Organization ID |

#### Success Response

```json
{
  "status": "success",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "My Organization",
    "description": "This is my organization",
    "domain": "example.com",
    "settings": {
      "color": "#FF5733",
      "logo": "https://example.com/logo.png"
    },
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Error Responses

Organization not found:

```json
{
  "status": "error",
  "message": "Organization not found"
}
```

Not a member of the organization:

```json
{
  "status": "error",
  "message": "You are not a member of this organization"
}
```

Authentication error:

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

### Update Organization

Updates an existing organization.

- **URL**: `/organizations/:id`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Permissions**: OrgAdmin or OrgEditor role

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string (UUID) | Yes | Organization ID |

#### Request Body

```json
{
  "name": "Updated Organization Name",
  "description": "This is an updated description",
  "domain": "updated-example.com",
  "settings": {
    "color": "#33FF57",
    "logo": "https://example.com/updated-logo.png"
  }
}
```

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `name` | string | No | Updated name of the organization |
| `description` | string | No | Updated description of the organization |
| `domain` | string | No | Updated domain associated with the organization |
| `settings` | object | No | Updated organization settings |

#### Success Response

```json
{
  "status": "success",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Updated Organization Name",
    "description": "This is an updated description",
    "domain": "updated-example.com",
    "settings": {
      "color": "#33FF57",
      "logo": "https://example.com/updated-logo.png"
    },
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Error Responses

Organization not found:

```json
{
  "status": "error",
  "message": "Organization not found"
}
```

Insufficient permissions:

```json
{
  "status": "error",
  "message": "You do not have permission to update this organization"
}
```

Authentication error:

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

### Delete Organization

Deletes an organization.

- **URL**: `/organizations/:id`
- **Method**: `DELETE`
- **Auth Required**: Yes
- **Permissions**: OrgAdmin role

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string (UUID) | Yes | Organization ID |

#### Success Response

```json
{
  "status": "success",
  "message": "Organization deleted successfully"
}
```

#### Error Responses

Organization not found:

```json
{
  "status": "error",
  "message": "Organization not found"
}
```

Insufficient permissions:

```json
{
  "status": "error",
  "message": "You do not have permission to delete this organization"
}
```

Authentication error:

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

### Get Organization Members

Returns all members of a specific organization.

- **URL**: `/organizations/:id/members`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions**: Member of the organization

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string (UUID) | Yes | Organization ID |

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | integer | No | 1 | Page number for pagination |
| `limit` | integer | No | 10 | Number of items per page (max: 50) |
| `name` | string | No | - | Filter members by name |
| `email` | string | No | - | Filter members by email |
| `role` | string | No | - | Filter members by role |

#### Success Response

```json
{
  "status": "success",
  "members": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "userId": "9669208d-b352-4258-bf59-e9b4a543fa16",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "orgadmin",
      "joinedAt": "2023-01-01T00:00:00Z",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    },
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "userId": "9669208d-b352-4258-bf59-e9b4a543fa17",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "role": "orguser",
      "joinedAt": "2023-01-02T00:00:00Z",
      "createdAt": "2023-01-02T00:00:00Z",
      "updatedAt": "2023-01-02T00:00:00Z"
    }
  ],
  "results": 2
}
```

#### Error Responses

Organization not found:

```json
{
  "status": "error",
  "message": "Organization not found"
}
```

Not a member of the organization:

```json
{
  "status": "error",
  "message": "You are not a member of this organization"
}
```

Authentication error:

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

### Invite Member

Invites a new member to the organization.

- **URL**: `/organizations/:id/members`
- **Method**: `POST`
- **Auth Required**: Yes
- **Permissions**: OrgAdmin or OrgEditor role

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string (UUID) | Yes | Organization ID |

#### Request Body

```json
{
  "email": "<EMAIL>",
  "role": "orguser"
}
```

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `email` | string | Yes | Email address of the person to invite |
| `role` | string | Yes | Role to assign to the new member (orgadmin, orgeditor, orgagent, orguser, orgviewer) |

#### Success Response

```json
{
  "status": "success",
  "message": "Invitation sent successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "role": "orguser",
    "status": "pending",
    "expiresAt": "2023-01-08T00:00:00Z",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Error Responses

Organization not found:

```json
{
  "status": "error",
  "message": "Organization not found"
}
```

Insufficient permissions:

```json
{
  "status": "error",
  "message": "You do not have permission to invite members to this organization"
}
```

Invalid email:

```json
{
  "status": "error",
  "message": "Invalid email format"
}
```

User already a member:

```json
{
  "status": "error",
  "message": "User is already a member of this organization"
}
```

Authentication error:

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

### Update Member Role

Updates the role of an existing organization member.

- **URL**: `/organizations/:id/members/:member_id`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Permissions**: OrgAdmin role

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string (UUID) | Yes | Organization ID |
| `member_id` | string (UUID) | Yes | Member ID |

#### Request Body

```json
{
  "role": "orgeditor"
}
```

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `role` | string | Yes | New role to assign to the member (orgadmin, orgeditor, orgagent, orguser, orgviewer) |

#### Success Response

```json
{
  "status": "success",
  "message": "Member role updated successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "userId": "9669208d-b352-4258-bf59-e9b4a543fa17",
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "role": "orgeditor",
    "joinedAt": "2023-01-02T00:00:00Z",
    "createdAt": "2023-01-02T00:00:00Z",
    "updatedAt": "2023-01-03T00:00:00Z"
  }
}
```

#### Error Responses

Organization not found:

```json
{
  "status": "error",
  "message": "Organization not found"
}
```

Member not found:

```json
{
  "status": "error",
  "message": "Member not found"
}
```

Insufficient permissions:

```json
{
  "status": "error",
  "message": "You do not have permission to update member roles in this organization"
}
```

Invalid role:

```json
{
  "status": "error",
  "message": "Invalid role. Must be one of: orgadmin, orgeditor, orgagent, orguser, orgviewer"
}
```

Authentication error:

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

### Remove Member

Removes a member from the organization.

- **URL**: `/organizations/:id/members/:member_id`
- **Method**: `DELETE`
- **Auth Required**: Yes
- **Permissions**: OrgAdmin role

#### URL Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string (UUID) | Yes | Organization ID |
| `member_id` | string (UUID) | Yes | Member ID |

#### Success Response

```json
{
  "status": "success",
  "message": "Member removed successfully"
}
```

#### Error Responses

Organization not found:

```json
{
  "status": "error",
  "message": "Organization not found"
}
```

Member not found:

```json
{
  "status": "error",
  "message": "Member not found"
}
```

Insufficient permissions:

```json
{
  "status": "error",
  "message": "You do not have permission to remove members from this organization"
}
```

Cannot remove self:

```json
{
  "status": "error",
  "message": "You cannot remove yourself from the organization. Transfer ownership first or delete the organization."
}
```

Authentication error:

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

### Accept Invitation

Accepts an invitation to join an organization.

- **URL**: `/organizations/invitations/accept`
- **Method**: `POST`
- **Auth Required**: Yes
- **Permissions**: User or Admin role

#### Request Body

```json
{
  "token": "invitation_token_here"
}
```

#### Success Response

```json
{
  "status": "success",
  "message": "Invitation accepted successfully",
  "data": {
    "organizationId": "123e4567-e89b-12d3-a456-************",
    "organizationName": "My Organization",
    "role": "orguser"
  }
}
```

### Get User Invitations

Returns all pending invitations for the authenticated user.

- **URL**: `/organizations/invitations`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions**: User or Admin role

#### Success Response

```json
{
  "status": "success",
  "invitations": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "organizationId": "123e4567-e89b-12d3-a456-************",
      "organizationName": "My Organization",
      "email": "<EMAIL>",
      "role": "orguser",
      "status": "pending",
      "expiresAt": "2023-01-08T00:00:00Z",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  ],
  "results": 1
}
```

### Get Organization Invitations

Returns all pending invitations for a specific organization.

- **URL**: `/organizations/:id/invitations`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions**: OrgAdmin or OrgEditor role

#### Success Response

```json
{
  "status": "success",
  "invitations": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "email": "<EMAIL>",
      "role": "orguser",
      "status": "pending",
      "invitedBy": {
        "id": "9669208d-b352-4258-bf59-e9b4a543fa16",
        "name": "John Doe"
      },
      "expiresAt": "2023-01-08T00:00:00Z",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  ],
  "results": 1
}
```

### Set Default Organization

Sets the default organization for the authenticated user.

- **URL**: `/organizations/default/:id`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Permissions**: Member of the organization

#### Success Response

```json
{
  "status": "success",
  "message": "Default organization set successfully",
  "data": {
    "organizationId": "123e4567-e89b-12d3-a456-************",
    "organizationName": "My Organization"
  }
}
```
