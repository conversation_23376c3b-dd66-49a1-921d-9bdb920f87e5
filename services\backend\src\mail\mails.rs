use super::sendmail::{send_email, send_email_with_logos};

pub async fn send_welcome_email(
    to_email: &str,
    username: &str
) -> Result<(), Box<dyn std::error::Error>> {
    let subject = "Welcome to Application";
    let template_path = "src/mail/templates/Welcome-email.html";
    let placeholders = vec![
        ("{{username}}".to_string(), username.to_string())
    ];

    send_email(to_email, subject, template_path, &placeholders).await
}

pub async fn send_organization_invitation_email(
    to_email: &str,
    inviter_name: &str,
    organization_name: &str,
    role: &str,
    invitation_link: &str
) -> Result<(), Box<dyn std::error::Error>> {
    let subject = format!("Invitation to join {} organization", organization_name);
    let template_path = "src/mail/templates/organization-invitation-email.html";
    let placeholders = vec![
        ("{{inviter_name}}".to_string(), inviter_name.to_string()),
        ("{{organization_name}}".to_string(), organization_name.to_string()),
        ("{{role}}".to_string(), role.to_string()),
        ("{{invitation_link}}".to_string(), invitation_link.to_string())
    ];

    send_email(to_email, &subject, template_path, &placeholders).await
}

pub async fn send_forgot_password_email(
    to_email: &str,
    rest_link: &str,
    username: &str
) -> Result<(), Box<dyn std::error::Error>> {
    let subject = "Rest your Password";
    let template_path = "src/mail/templates/RestPassword-email.html";
    let placeholders = vec![
        ("{{username}}".to_string(), username.to_string()),
        ("{{rest_link}}".to_string(), rest_link.to_string())
    ];

    send_email(to_email, subject, template_path, &placeholders).await
}

pub async fn send_otp_email(
    to_email: &str,
    username: &str,
    otp_code: &str
) -> Result<(), Box<dyn std::error::Error>> {
    let subject = "Your Lola OTP — It's Time to Log In ✨";
    let template_path = "src/mail/templates/lola-OTP-email.html";

    // Split the OTP code into individual digits
    let mut placeholders = vec![
        ("{{username}}".to_string(), username.to_string()),
        ("{{otp_code}}".to_string(), otp_code.to_string())
    ];

    // Add individual digit placeholders
    let digits: Vec<char> = otp_code.chars().collect();
    if digits.len() >= 6 {
        placeholders.push(("{{digit_1}}".to_string(), digits[0].to_string()));
        placeholders.push(("{{digit_2}}".to_string(), digits[1].to_string()));
        placeholders.push(("{{digit_3}}".to_string(), digits[2].to_string()));
        placeholders.push(("{{digit_4}}".to_string(), digits[3].to_string()));
        placeholders.push(("{{digit_5}}".to_string(), digits[4].to_string()));
        placeholders.push(("{{digit_6}}".to_string(), digits[5].to_string()));
    }

    // Define logo paths for embedding in the email
    let logo_black_path = "images/LolaLogoBlack.png";
    let logo_white_path = "images/LolaLogoWhite.png";

    // Print the current working directory for debugging
    println!("Current working directory: {:?}", std::env::current_dir().unwrap_or_default());

    send_email_with_logos(to_email, subject, template_path, &placeholders, logo_black_path, logo_white_path).await
}