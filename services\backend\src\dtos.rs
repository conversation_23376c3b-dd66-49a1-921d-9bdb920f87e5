use core::str;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::Validate;

use crate::models::{Organization, OrganizationMember, OrganizationRole, User, UserRole};

#[derive(<PERSON><PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RequestOtpDto {
    #[validate(
        length(min = 1, message = "Email is required"),
        email(message = "Email is invalid")
    )]
    pub email: String,
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VerifyOtpDto {
    #[validate(
        length(min = 1, message = "Email is required"),
        email(message = "Email is invalid")
    )]
    pub email: String,

    #[validate(
        length(min = 6, max = 6, message = "OTP code must be 6 digits")
    )]
    pub otp_code: String,

    // Name is required only for new users
    pub name: Option<String>,

    // New field for storing profile data
    pub profile_data: Option<serde_json::Value>,
}

#[derive(Serialize, Deserialize, Validate)]
pub struct RequestQueryDto {
    #[validate(range(min = 1))]
    pub page: Option<usize>,
    #[validate(range(min = 1, max = 50))]
    pub limit: Option<usize>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FilterUserDto {
    pub id: String,
    pub name: String,
    pub email: String,
    pub role: String,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub profile_data: Option<serde_json::Value>,
}

impl FilterUserDto {
    pub fn filter_user(user: &User) -> Self {
        FilterUserDto {
            id: user.id.to_string(),
            name: user.name.to_owned(),
            email: user.email.to_owned(),
            role: user.role.to_str().to_string(),
            created_at: user.created_at.unwrap(),
            updated_at: user.updated_at.unwrap(),
            profile_data: user.profile_data.clone(),
        }
    }

    pub fn filter_users(user: &[User]) -> Vec<FilterUserDto> {
        user.iter().map(FilterUserDto::filter_user).collect()
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserData {
    pub user: FilterUserDto,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserResponseDto {
    pub status: String,
    pub data: UserData,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserListResponseDto {
    pub status: String,
    pub users: Vec<FilterUserDto>,
    pub results: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RequestOtpResponseDto {
    pub status: String,
    pub message: String,
    pub userType: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub signup_text: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VerifyOtpResponseDto {
    pub status: String,
    pub token: String,
    pub userType: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
}

#[derive(Serialize, Deserialize)]
pub struct Response {
    pub status: &'static str,
    pub message: String,
}

#[derive(Validate, Debug, Default, Clone, Serialize, Deserialize)]
pub struct NameUpdateDto {
    #[validate(length(min = 1, message = "Name is required"))]
    pub name: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct RoleUpdateDto {
    #[validate(custom = "validate_user_role")]
    pub role: UserRole,
}

fn validate_user_role(_role: &UserRole) -> Result<(), validator::ValidationError> {
    // Since we're using an enum with only two variants, we can simplify this
    Ok(())
}



// Resend OTP DTO
#[derive(Validate, Debug, Default, Clone, Serialize, Deserialize)]
pub struct ResendOtpDto {
    #[validate(
        length(min = 1, message = "Email is required"),
        email(message = "Email is invalid")
    )]
    pub email: String,
}

// Reset Account Request DTO
#[derive(Validate, Debug, Default, Clone, Serialize, Deserialize)]
pub struct ResetAccountRequestDto {
    #[validate(
        length(min = 1, message = "Email is required"),
        email(message = "Email is invalid")
    )]
    pub email: String,
}

// Reset Account Verify DTO
#[derive(Validate, Debug, Default, Clone, Serialize, Deserialize)]
pub struct ResetAccountVerifyDto {
    #[validate(
        length(min = 1, message = "Email is required"),
        email(message = "Email is invalid")
    )]
    pub email: String,

    #[validate(
        length(min = 6, max = 6, message = "OTP code must be 6 digits")
    )]
    pub otp_code: String,
}

// Reset Account Response DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct ResetAccountResponseDto {
    pub status: String,
    pub message: String,
}

// User Profile Update DTO
#[derive(Validate, Debug, Default, Clone, Serialize, Deserialize)]
pub struct UserProfileUpdateDto {
    #[validate(length(min = 1, message = "Name is required"))]
    pub name: String,
    pub profile_data: Option<serde_json::Value>,
}

// Organization DTOs

#[derive(Validate, Debug, Default, Clone, Serialize, Deserialize)]
pub struct CreateOrganizationDto {
    #[validate(length(min = 1, message = "Organization name is required"))]
    pub name: String,
    pub description: Option<String>,
    pub domain: Option<String>,
    pub settings: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FilterOrganizationDto {
    pub id: String,
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub domain: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

impl FilterOrganizationDto {
    pub fn filter_organization(org: &Organization) -> Self {
        FilterOrganizationDto {
            id: org.id.to_string(),
            name: org.name.to_owned(),
            description: org.description.clone(),
            domain: org.domain.clone(),
            settings: org.settings.clone(),
            created_at: org.created_at.unwrap_or_else(|| chrono::Utc::now()),
            updated_at: org.updated_at.unwrap_or_else(|| chrono::Utc::now()),
        }
    }

    pub fn filter_organizations(orgs: &[Organization]) -> Vec<FilterOrganizationDto> {
        orgs.iter().map(FilterOrganizationDto::filter_organization).collect()
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrganizationData {
    pub organization: FilterOrganizationDto,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrganizationResponseDto {
    pub status: String,
    pub data: OrganizationData,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrganizationListResponseDto {
    pub status: String,
    pub organizations: Vec<FilterOrganizationDto>,
    pub results: i64,
}

#[derive(Validate, Debug, Default, Clone, Serialize, Deserialize)]
pub struct InviteMemberDto {
    #[validate(
        length(min = 1, message = "Email is required"),
        email(message = "Email is invalid")
    )]
    pub email: String,
    pub role: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FilterOrganizationMemberDto {
    pub id: String,
    pub organization_id: String,
    pub user_id: String,
    pub role: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub invited_by: Option<String>,
    pub joined_at: Option<DateTime<Utc>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "createdAt")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<DateTime<Utc>>,
    // Additional fields for display
    pub user_name: String,
    pub user_email: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrganizationMemberData {
    pub member: FilterOrganizationMemberDto,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrganizationMemberResponseDto {
    pub status: String,
    pub data: OrganizationMemberData,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrganizationMemberListResponseDto {
    pub status: String,
    pub members: Vec<FilterOrganizationMemberDto>,
    pub results: i64,
}

#[derive(Validate, Debug, Default, Clone, Serialize, Deserialize)]
pub struct UpdateMemberRoleDto {
    pub member_id: String,
    pub role: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FilterOrganizationInvitationDto {
    pub id: String,
    pub organization_id: String,
    pub email: String,
    pub role: String,
    pub invited_by: String,
    pub token: String,
    pub expires_at: DateTime<Utc>,
    pub status: String,
    #[serde(rename = "createdAt")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<DateTime<Utc>>,
    // Additional fields for display
    pub organization_name: String,
    pub invited_by_name: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrganizationInvitationResponseDto {
    pub status: String,
    pub invitation: FilterOrganizationInvitationDto,
}

#[derive(Validate, Debug, Default, Clone, Serialize, Deserialize)]
pub struct AcceptInvitationDto {
    pub token: String,
}


