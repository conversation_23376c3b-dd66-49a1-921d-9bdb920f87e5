[package]
name = "lola-backend"
version = "0.1.0"
edition = "2021"

[lib]
name = "lola_backend"
path = "src/lib.rs"

[[bin]]
name = "lola-backend"
path = "src/main.rs"

[dependencies]
argon2 = "0.5.1"
chrono = { version = "0.4.26", features = ["serde"] }
dotenv = "0.15.0"
jsonwebtoken = "9.2.0"
serde = { version = "1.0.183", features = ["derive"] }
serde_json = "1.0.104"
sqlx = { version = "0.8.5", features = ["runtime-async-std-native-tls", "postgres", "chrono", "uuid"] }
uuid = { version = "1.4.1", features = ["serde", "v4"] }
validator = { version = "0.16.1", features = ["derive"] }
axum = { version = "0.7.5" }
axum-extra = { version = "0.9.3", features = ["cookie"]}
tokio = { version = "1.39.3", features = ["full"] }
tower = "0.5.0"
time = "0.3.20"
tower-http = { version = "0.5.2", features = ["cors","trace"] }
tracing-subscriber = { version = "0.3.18"}
lettre = "0.11.7"
async-trait = "0.1.86"
reqwest = { version = "0.11", features = ["json"] }
totp-rs = "5.5.1"
base64 = "0.21.7"
base32 = "0.4.0"
getrandom = "0.2.15"
qrcode = "0.13.0"
image = { version = "0.24.8", features = ["png"] }
rand = "0.8.5"
mime = "0.3.17"

