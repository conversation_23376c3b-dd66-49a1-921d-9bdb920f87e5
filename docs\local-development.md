# Local Development

This guide covers the local development workflow for the Saday Agent platform.

## Project Structure

The Saday Agent platform is organized as a monorepo with the following structure:

```
saday-agent/
├── agent-web/           # Web frontend (Next.js)
├── agent-native/        # Mobile frontend (React Native)
├── packages/
│   └── apatiteui/       # Shared UI component library
├── services/
│   ├── backend/         # Backend API (Rust)
│   └── agent-service/   # Agent orchestration service (Rust)
├── docs/                # Documentation
└── docker-compose.yml   # Docker Compose configuration
```

## Development Workflow

### UI Library Development

#### Building the UI Library

```bash
# From the root of the monorepo
pnpm build:ui
```

#### Watching for Changes

```bash
# From the root of the monorepo
pnpm dev:ui
```

The UI library is built using TypeScript and bundled with tsup. It exports components that work in both React Native and Next.js environments.

### Backend Development

#### Running the Backend Locally

```bash
# Navigate to the backend directory
cd services/backend

# Run in development mode (with auto-reload)
cargo watch -x run

# Or run normally
cargo run
```

The backend server will start on http://localhost:8001 by default.

#### Database Migrations

When making changes to the database schema:

1. Create a new migration:

```bash
# Create a new migration file
sqlx migrate add <migration_name>
```

2. Edit the generated SQL file in the `migrations` directory

3. Run the migration:

```bash
sqlx migrate run
```

4. Generate SQLx offline data (for builds without database connection):

```bash
cargo sqlx prepare --merged
```

#### Testing the Backend

```bash
# Run all tests
cargo test

# Run specific tests
cargo test <test_name>

# Run tests with output
cargo test -- --nocapture
```

### Web Frontend Development

#### Running the Web Frontend

```bash
# Navigate to the web frontend directory
cd agent-web

# Start the development server
pnpm dev
```

The web application will be available at http://localhost:3000.

#### Building for Production

```bash
# Build the web frontend
pnpm build

# Start the production server
pnpm start
```

### Mobile Frontend Development

#### Running the Mobile Frontend

```bash
# Navigate to the mobile frontend directory
cd agent-native

# Start the Expo development server
pnpm start

# Run on Android
pnpm android

# Run on iOS
pnpm ios
```

#### Building Mobile Apps

```bash
# Build for Android
pnpm build:android

# Build for iOS
pnpm build:ios
```

## Docker Development

For a containerized development environment:

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Rebuild a specific service
docker-compose build <service_name>

# Restart a specific service
docker-compose restart <service_name>
```

## API Development

When developing new API endpoints:

1. Define the data transfer objects (DTOs) in `services/backend/src/dtos.rs`
2. Implement the handler in `services/backend/src/handler/`
3. Add the route to the appropriate router in `services/backend/src/router.rs`
4. Update the API documentation in `services/backend/api_docs/API_DOCUMENTATION.md`

Example of adding a new endpoint:

```rust
// In dtos.rs
#[derive(Debug, Deserialize, Validate)]
pub struct CreateAgentDto {
    #[validate(length(min = 1, message = "Name is required"))]
    pub name: String,
    pub description: Option<String>,
}

// In handler/agent.rs
pub async fn create_agent(
    Extension(app_state): Extension<Arc<AppState>>,
    Extension(auth): Extension<JWTAuthMiddeware>,
    Json(body): Json<CreateAgentDto>,
) -> Result<impl IntoResponse, HttpError> {
    // Implementation
}

// In router.rs
pub fn agent_routes() -> Router {
    Router::new()
        .route("/", post(create_agent))
        // Other routes
        .route_layer(middleware::from_fn_with_state(
            app_state.clone(),
            auth,
        ))
}
```

## Code Style and Linting

### Rust

```bash
# Format Rust code
cargo fmt

# Lint Rust code
cargo clippy
```

### TypeScript/JavaScript

```bash
# Format code
pnpm lint

# Fix linting issues
pnpm lint:fix
```

## Debugging

### Backend Debugging

1. Add logging statements:

```rust
log::debug!("Debug information: {:?}", variable);
log::info!("Info message");
log::error!("Error occurred: {}", error);
```

2. Use VS Code debugging with the Rust extension

### Frontend Debugging

1. Use browser developer tools for web frontend
2. Use React DevTools for component inspection
3. For React Native, use Flipper or the Expo DevTools

## Working with the Agent System

When developing agent-related features:

1. Define agent models in `services/agent-service/src/models/`
2. Implement agent logic in `services/agent-service/src/agents/`
3. Add API endpoints in `services/agent-service/src/api/`
4. Test agent functionality with unit and integration tests

## Continuous Integration

The project uses GitHub Actions for CI/CD:

- Pull requests trigger test runs
- Merges to main trigger builds and deployments
- Code quality checks run on all PRs

## Best Practices

1. **Commit Messages**: Follow conventional commits format
2. **Branch Naming**: Use `feature/`, `bugfix/`, `docs/` prefixes
3. **Testing**: Write tests for all new features
4. **Documentation**: Update docs for API changes
5. **Code Reviews**: All PRs require at least one review

## Troubleshooting

### Common Development Issues

1. **Backend won't start**:
   - Check if PostgreSQL is running
   - Verify `.env` file configuration
   - Check for port conflicts

2. **Frontend build errors**:
   - Clear node_modules: `rm -rf node_modules && pnpm install`
   - Check for TypeScript errors
   - Verify API endpoint URLs

3. **Database migration issues**:
   - Check migration files for syntax errors
   - Ensure migrations are applied in the correct order
   - Use `sqlx database reset` to start fresh if needed
