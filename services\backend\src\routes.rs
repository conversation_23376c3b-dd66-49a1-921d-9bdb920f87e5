use std::sync::Arc;

use axum::{middleware, Extension, Router, routing::get};
use tower_http::trace::TraceLayer;

use crate::{
    handler::{auth::auth_handler, health::health_checker_handler, users::users_handler, organization::organization_handler},
    middleware::auth,
    AppState,
};

pub fn create_router(app_state: Arc<AppState>) -> Router {
    let api_route = Router::new()
        .route("/healthchecker", get(health_checker_handler))
        .nest("/auth", auth_handler())
        .nest(
            "/users",
            users_handler()
                .layer(middleware::from_fn(auth))
        )
        .nest(
            "/organizations",
            organization_handler(app_state.clone())
        )
        .layer(TraceLayer::new_for_http())
        .layer(Extension(app_state));

    Router::new().nest("/api", api_route)
}

