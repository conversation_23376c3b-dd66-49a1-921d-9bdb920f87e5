# Users Module

The Users module provides functionality for managing user accounts, profiles, and roles. It handles user data storage, retrieval, and updates.

## Features

- User profile management
- Role-based access control
- User data storage and retrieval
- User verification

## User Roles

The system supports the following user roles:

- **Admin**: Full access to the system, can manage users and settings
- **User**: Standard user with access to their own data and organizations

## User Profile

The user profile consists of the following information:

- **Name**: User's display name
- **Email**: User's email address (used for authentication)
- **Role**: User's role in the system (Admin or User)
- **Profile Data**: JSON object containing additional user data
  - Can include preferences, settings, and other user-specific information
  - Flexible structure allows for storing various types of data

## Database Schema

### Users Table

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role user_role NOT NULL DEFAULT 'user',
    verified BO<PERSON>EAN NOT NULL DEFAULT FALSE,
    verified_admin BOOLEAN NOT NULL DEFAULT FALSE,
    email_otp VARCHAR(6),
    email_otp_expires_at TIMESTAMPTZ,
    profile_data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

## API Endpoints

For detailed information about the Users API endpoints, see the [Users API Reference](../../api/users.md).

## Implementation Details

The Users module is implemented in the following files:

- `src/handler/users.rs` - HTTP request handlers
- `src/models/user.rs` - Database models
- `src/dtos.rs` - Data Transfer Objects
- `src/db/user.rs` - Database access functions

## User Data Security

The Users module implements the following security measures:

1. **Email Verification**: Users must verify their email address
2. **Role-Based Access Control**: Users can only access data they are authorized to see
3. **Data Filtering**: Sensitive user data is filtered before being sent to the client
4. **JWT Authentication**: All user endpoints require authentication

## User Profile Management

### Updating User Profile

Users can update their profile information, including:

- **Name**: Display name
- **Profile Data**: Additional user data stored as JSON

The profile data is stored as a JSONB object in the database, which allows for flexible storage of user-specific information without requiring database schema changes.

Example profile data structure:

```json
{
  "preferences": {
    "theme": "dark",
    "language": "en",
    "notifications": {
      "email": true,
      "push": false
    }
  },
  "bio": "Software developer with 5 years of experience",
  "location": "San Francisco, CA",
  "social": {
    "twitter": "@username",
    "github": "username",
    "linkedin": "username"
  }
}
```

## User Verification

The system supports two types of user verification:

1. **Email Verification**: Automatically set to true when a user successfully verifies their email during authentication
2. **Admin Verification**: Can be set by administrators to verify users for additional privileges

## Best Practices

1. **Minimal Data Collection**: Only collect user data that is necessary for the application
2. **Data Filtering**: Always filter sensitive user data before sending it to the client
3. **Role Assignment**: Assign the minimum necessary role to each user
4. **Regular Audits**: Periodically review user accounts and their roles

## Troubleshooting

### Common Issues

1. **User Not Found**
   - Verify that the user ID or email is correct
   - Check if the user has been deleted

2. **Permission Denied**
   - Verify the user's role
   - Check if the user is trying to access data they are not authorized to see

3. **Profile Update Failed**
   - Ensure the profile data is properly formatted
   - Check if the user is authenticated
