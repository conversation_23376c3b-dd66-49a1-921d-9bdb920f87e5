-- Add user_fields column to users table
ALTER TABLE users ADD COLUMN user_fields JSONB DEFAULT '{}'::jsonb;

-- Add an index on the user_fields column for better query performance
CREATE INDEX idx_users_user_fields ON users USING GIN (user_fields);

-- Comment to explain the purpose of this migration
COMMENT ON COLUMN users.user_fields IS 'JSON field to store additional user data that is not essential for authentication';
