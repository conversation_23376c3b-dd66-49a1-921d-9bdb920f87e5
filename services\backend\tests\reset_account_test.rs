use dotenv::dotenv;
use reqwest::Client;
use serde_json::{json, Value};
use std::io;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenv().ok();

    let client = Client::new();
    let base_url = std::env::var("API_URL").unwrap_or_else(|_| "http://localhost:8001/api".to_string());

    println!("=== Testing Account Reset API ===");
    println!("Enter email to reset: ");
    let mut email = String::new();
    io::stdin().read_line(&mut email)?;
    let email = email.trim();

    // Step 1: Request OTP for account reset
    println!("\nRequesting OTP for account reset...");
    let response = client
        .post(&format!("{}/auth/reset-account", base_url))
        .json(&json!({
            "email": email
        }))
        .send()
        .await?;

    let status = response.status();
    let body: Value = response.json().await?;
    println!("Status: {}", status);
    println!("Response: {}", serde_json::to_string_pretty(&body)?);

    if !status.is_success() {
        return Ok(());
    }

    // Step 2: Verify OTP for account reset
    println!("\nEnter the OTP sent to your email: ");
    let mut otp = String::new();
    io::stdin().read_line(&mut otp)?;
    let otp = otp.trim();

    println!("\nVerifying OTP for account reset...");
    let response = client
        .post(&format!("{}/auth/verify-reset", base_url))
        .json(&json!({
            "email": email,
            "otp_code": otp
        }))
        .send()
        .await?;

    let status = response.status();
    let body: Value = response.json().await?;
    println!("Status: {}", status);
    println!("Response: {}", serde_json::to_string_pretty(&body)?);

    if !status.is_success() {
        return Ok(());
    }

    println!("\nAccount reset successful!");
    println!("You can now log in as a new user with the same email.");

    Ok(())
}
