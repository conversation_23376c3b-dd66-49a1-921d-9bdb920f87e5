$headers = @{
    "Content-Type" = "application/json"
}

$body = @{
    "email" = "<EMAIL>"
} | ConvertTo-Json

Write-Host "Testing /auth/resend-otp endpoint..."
$response = Invoke-RestMethod -Uri "http://localhost:8001/api/auth/resend-otp" -Method Post -Headers $headers -Body $body
$response | ConvertTo-Json

# Save the response for later use
$response | ConvertTo-Json | Out-File -FilePath "resend_otp_response.json"

Write-Host "Check your email for the new OTP code that was sent."
