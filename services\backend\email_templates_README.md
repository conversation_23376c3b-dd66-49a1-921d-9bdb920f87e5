# Email Templates Editing Guide

This guide will help you edit and preview the email templates used in the Lola application.

## Email Delivery Configuration

The application uses Zoho SMTP for email delivery with the following setup:
- Direct TLS connection on port 465
- Custom DNS configuration using Google DNS (8.8.8.8, 8.8.4.4)
- Secure transport encryption

### SMTP Configuration
Required environment variables:
```
SMTP_USERNAME=<zoho-smtp-username>
SMTP_PASSWORD=<zoho-smtp-password>
SMTP_SERVER=<zoho-smtp-server>
SMTP_PORT=465
```

## Available Templates

The application uses the following email templates:

1. **OTP Email** (`src/mail/templates/OTP-email.html`)
   - Sent when a user requests an OTP code
   - Enhanced version available with dark mode support
   - Supports individual digit placeholders

2. **Welcome Email** (`src/mail/templates/Welcome-email.html`)
   - Sent when a user completes registration
   - Modern, responsive design

3. **Verification Email** (`src/mail/templates/Verification-email.html`)
   - Sent for email verification
   - Includes secure verification link

4. **Reset Password Email** (`src/mail/templates/RestPassword-email.html`)
   - Sent when a user requests a password reset
   - Contains secure reset link with 30-minute expiration

## Enhanced Templates

We've created enhanced versions of these templates with better styling in the `src/mail/templates/enhanced/` directory:

- Improved visual design
- Better email client compatibility
- Responsive layouts that work on all devices
- Support for dark mode (OTP template)
- Consistent branding elements

### Key Features
- Inline CSS for maximum compatibility
- Table-based layouts for consistent rendering
- Web-safe fonts
- Fallback styles for older email clients
- Optimized for both desktop and mobile viewing

## Tools for Editing and Previewing

### 1. Email Template Preview Tool

The `email_template_preview.html` file provides a simple web interface for previewing and editing email templates.

**How to use:**
1. Open `email_template_preview.html` in your web browser
2. Select the template you want to edit from the tabs
3. Make changes to the HTML in the editor
4. Click "Update Preview" to see how your changes look
5. When you're satisfied, copy the HTML and update the actual template file

### 2. Template Testing Script

The `test_email_templates.ps1` PowerShell script helps you generate preview files for all templates with sample data.

**How to use:**
1. Run the script in PowerShell: `./test_email_templates.ps1`
2. The script will create an `email_previews` directory with HTML files for each template
3. Open these HTML files in your browser to see how the emails will look

## Tips for Email Template Design

1. **Use inline CSS**: Email clients often strip out `<style>` tags, so use inline styles for all elements
2. **Keep it simple**: Complex layouts may not render correctly in all email clients
3. **Test in multiple clients**: If possible, test your templates in different email clients (Gmail, Outlook, etc.)
4. **Use tables for layout**: While not ideal for web, tables provide the most consistent layout in email clients
5. **Use web-safe fonts**: Stick to fonts like Arial, Verdana, Georgia, Times New Roman, etc.

## Implementing Your Changes

After editing the templates, you need to update the actual template files in the `src/mail/templates/` directory. You can either:

1. Directly edit the existing template files
2. Replace them with the enhanced versions from `src/mail/templates/enhanced/`

## Placeholders

The templates use the following placeholders that will be replaced with actual data:

- `{{username}}` - The user's name
- `{{otp_code}}` - The OTP code (for OTP emails)
- `{{verification_link}}` - The verification link (for verification emails)
- `{{rest_link}}` - The password reset link (for reset password emails)

Make sure to keep these placeholders in your templates.
