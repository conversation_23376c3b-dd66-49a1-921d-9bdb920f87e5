mod common;

use axum::{
    body::Body,
    http::{Method, StatusCode},
};
use chrono::NaiveDate;
use common::{create_json_request, create_test_router, extract_json_response, load_env};
use serde_json::json;
use std::io::{self, Write};
use tower::ServiceExt;

#[tokio::test]
#[ignore] // This test is ignored by default and only run when explicitly requested
async fn test_signup_flow() {
    // Load environment variables
    load_env();

    // Create the test router
    let app = create_test_router();

    // Step 1: Get email from user
    println!("\n=== INTERACTIVE SIGNUP TEST ===");
    print!("Enter your email address: ");
    io::stdout().flush().unwrap();

    let mut email = String::new();
    io::stdin().read_line(&mut email).expect("Failed to read email");
    let email = email.trim();

    // Step 2: Request OTP
    println!("\nSending OTP to {}", email);

    let request_body = json!({
        "email": email
    });

    let request = create_json_request(Method::POST, "http://localhost:8001/api/auth/request-otp", &request_body);
    let response = app.clone().oneshot(request).await.unwrap();

    let (status, json_response) = extract_json_response(response).await;

    if status == StatusCode::TOO_MANY_REQUESTS {
        println!("\n⚠️ Rate limit exceeded. Please try again later.");
        println!("Error: {}", json_response["message"]);
        return;
    }

    assert_eq!(status, StatusCode::OK);
    assert_eq!(json_response["status"], "success");
    assert_eq!(json_response["message"], "OTP sent to your email");

    // Step 3: Prompt user for OTP
    println!("\nPlease check your email ({}) for the OTP", email);
    print!("Enter the OTP you received: ");
    io::stdout().flush().unwrap();

    let mut otp = String::new();
    io::stdin().read_line(&mut otp).expect("Failed to read OTP");
    let otp = otp.trim();

    // Step 4: Get name for new user (optional)
    print!("Enter your name (or press Enter to use default): ");
    io::stdout().flush().unwrap();

    let mut name = String::new();
    io::stdin().read_line(&mut name).expect("Failed to read name");
    let name = name.trim();

    // If name is empty, don't include it in the request
    let name_option = if name.is_empty() { None } else { Some(name) };

    // Step 5: Get date of birth (optional)
    print!("Enter your date of birth (YYYY-MM-DD) or press Enter to skip: ");
    io::stdout().flush().unwrap();

    let mut dob = String::new();
    io::stdin().read_line(&mut dob).expect("Failed to read date of birth");
    let dob = dob.trim();

    // Step 6: Get why_lola preference (optional)
    println!("Why are you using Lola?");
    println!("1. Meditation");
    println!("2. Journaling");
    println!("3. Both");
    print!("Enter your choice (1-3) or press Enter to skip: ");
    io::stdout().flush().unwrap();

    let mut why_lola_choice = String::new();
    io::stdin().read_line(&mut why_lola_choice).expect("Failed to read why_lola choice");
    let why_lola_choice = why_lola_choice.trim();

    // Step 7: Verify OTP and create account
    println!("\nVerifying OTP and creating account...");

    // Convert date of birth string to NaiveDate if provided
    let date_of_birth = if !dob.is_empty() {
        match NaiveDate::parse_from_str(dob, "%Y-%m-%d") {
            Ok(date) => Some(date),
            Err(_) => {
                println!("Invalid date format. Date of birth will be skipped.");
                None
            }
        }
    } else {
        None
    };

    // Convert why_lola choice to string value
    let why_lola = match why_lola_choice {
        "1" => Some("meditation".to_string()),
        "2" => Some("journaling".to_string()),
        "3" => Some("both".to_string()),
        _ => None
    };

    // Create the request body with optional fields
    let mut verify_body = json!({
        "email": email,
        "otp_code": otp
    });

    // Add name if provided
    if let Some(name_value) = name_option {
        verify_body["name"] = json!(name_value);
    }

    // Add date_of_birth if provided
    if let Some(dob) = date_of_birth {
        verify_body["date_of_birth"] = json!(dob.format("%Y-%m-%d").to_string());
    }

    // Add why_lola if provided
    if let Some(wl) = why_lola {
        verify_body["why_lola"] = json!(wl);
    }

    // Create a user_fields object with additional data
    let user_fields = json!({
        "test_field": "test_value",
        "signup_source": "interactive_test"
    });

    // Add user_fields to the request
    verify_body["user_fields"] = user_fields;

    let verify_request = create_json_request(Method::POST, "http://localhost:8001/api/auth/verify-otp", &verify_body);
    let verify_response = app.oneshot(verify_request).await.unwrap();

    let (status, verify_json) = extract_json_response(verify_response).await;

    // Print the raw response for debugging
    println!("\nResponse status: {}", status);
    println!("Response body: {}", verify_json);

    if status == StatusCode::OK || status == StatusCode::CREATED {
        // Check if the response contains a token (successful signup)
        if verify_json.get("token").is_some() {
            println!("\n✅ Account created successfully!");
            println!("JWT Token: {}", verify_json["token"].as_str().unwrap());
        }
        // Check if the message indicates success but without a token
        else if let Some(message) = verify_json.get("message") {
            if message.as_str().unwrap_or("").contains("success") ||
               message.as_str().unwrap_or("").contains("created") {
                println!("\n✅ Account created successfully!");
                println!("Message: {}", message);
            } else {
                println!("\n⚠️ Unexpected success message: {}", message);
            }
        }
        // Otherwise, it's a success response but we don't know what it means
        else {
            println!("\n✅ Success response received: {}", verify_json);
        }
    } else {
        println!("\n❌ Account creation failed: {}", verify_json);
        panic!("Account creation failed");
    }
}

#[tokio::test]
#[ignore] // This test is ignored by default and only run when explicitly requested
async fn test_signin_flow() {
    // Load environment variables
    load_env();

    // Create the test router
    let app = create_test_router();

    // Step 1: Get email from user
    println!("\n=== INTERACTIVE SIGNIN TEST ===");
    print!("Enter your email address: ");
    io::stdout().flush().unwrap();

    let mut email = String::new();
    io::stdin().read_line(&mut email).expect("Failed to read email");
    let email = email.trim();

    // Step 2: Request OTP
    println!("\nSending OTP to {}", email);

    let request_body = json!({
        "email": email
    });

    let request = create_json_request(Method::POST, "http://localhost:8001/api/auth/request-otp", &request_body);
    let response = app.clone().oneshot(request).await.unwrap();

    let (status, json_response) = extract_json_response(response).await;

    if status == StatusCode::TOO_MANY_REQUESTS {
        println!("\n⚠️ Rate limit exceeded. Please try again later.");
        println!("Error: {}", json_response["message"]);
        return;
    }

    assert_eq!(status, StatusCode::OK);
    assert_eq!(json_response["status"], "success");
    assert_eq!(json_response["message"], "OTP sent to your email");

    // Step 3: Prompt user for OTP
    println!("\nPlease check your email ({}) for the OTP", email);
    print!("Enter the OTP you received: ");
    io::stdout().flush().unwrap();

    let mut otp = String::new();
    io::stdin().read_line(&mut otp).expect("Failed to read OTP");
    let otp = otp.trim();

    // Step 4: Verify OTP
    println!("\nVerifying OTP...");

    let verify_body = json!({
        "email": email,
        "otp_code": otp
    });

    let verify_request = create_json_request(Method::POST, "http://localhost:8001/api/auth/verify-otp", &verify_body);
    let verify_response = app.oneshot(verify_request).await.unwrap();

    let (status, verify_json) = extract_json_response(verify_response).await;

    // Print the raw response for debugging
    println!("\nResponse status: {}", status);
    println!("Response body: {}", verify_json);

    if status == StatusCode::OK || status == StatusCode::CREATED {
        // Check if the response contains a token (successful signin)
        if verify_json.get("token").is_some() {
            println!("\n✅ Sign-in successful!");
            println!("JWT Token: {}", verify_json["token"].as_str().unwrap());
        }
        // Check if the message indicates success but without a token
        else if let Some(message) = verify_json.get("message") {
            if message.as_str().unwrap_or("").contains("success") ||
               message.as_str().unwrap_or("").contains("created") {
                println!("\n✅ Sign-in successful!");
                println!("Message: {}", message);
            } else {
                println!("\n⚠️ Unexpected success message: {}", message);
            }
        }
        // Otherwise, it's a success response but we don't know what it means
        else {
            println!("\n✅ Success response received: {}", verify_json);
        }
    } else {
        println!("\n❌ Sign-in failed: {}", verify_json);
        panic!("Sign-in failed");
    }
}
