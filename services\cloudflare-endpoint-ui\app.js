const express = require("express")
const axios = require("axios")
const bodyParser = require("body-parser")
const cors = require("cors")
const jwt = require("jsonwebtoken")
require("dotenv").config()

const app = express()
app.use(bodyParser.urlencoded({ extended: true }))
app.use(bodyParser.json())
app.use(
  cors({
    origin: process.env.CLIENT_ORIGIN || "https://localhost:3001",
    credentials: true,
  }),
)

// Environment variables should be used for sensitive information
const CLOUDFLARE_API_TOKEN = process.env.CLOUDFLARE_API_TOKEN
const ACCOUNT_ID = process.env.CLOUDFLARE_ACCOUNT_ID || process.env.ACCOUNT_ID
const TUNNEL_ID = process.env.CLOUDFLARE_TUNNEL_ID || process.env.TUNNEL_ID
const ZONE_ID = process.env.CLOUDFLARE_ZONE_ID || process.env.ZONE_ID
const JWT_SECRET = process.env.JWT_SECRET_KEY
const API_URL = process.env.API_URL || "https://api.drainedcat.in/api"

// Cloudflare API client
const cloudflareAPI = axios.create({
  baseURL: "https://api.cloudflare.com/client/v4",
  headers: {
    Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
    "Content-Type": "application/json",
  },
})

// Authentication middleware for API routes
const authenticateToken = async (req, res, next) => {
  // Health check endpoint doesn't require authentication
  if (req.path === "/health") {
    return next()
  }

  const authHeader = req.headers["authorization"]
  const token = authHeader && authHeader.split(" ")[1]

  if (!token) {
    return res.status(401).json({ status: "error", message: "No token provided" })
  }

  try {
    // Verify token using the same secret as your backend
    const decoded = jwt.verify(token, JWT_SECRET)
    console.log("Decoded JWT token:", JSON.stringify(decoded, null, 2))

    // The token doesn't contain role or verified_admin, so we need to fetch the user profile
    try {
      console.log("Fetching user profile from main API...")
      const response = await axios.get(`${API_URL}/users/me`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.data && response.data.data && response.data.data.user) {
        const user = response.data.data.user
        console.log("User profile fetched:", JSON.stringify(user, null, 2))

        // Add the user profile to the request
        req.user = {
          ...decoded,
          ...user,
        }

        next()
      } else {
        console.error("Invalid user profile response:", response.data)
        return res.status(401).json({ status: "error", message: "Invalid user profile" })
      }
    } catch (profileError) {
      console.error("Error fetching user profile:", profileError.message)
      return res.status(401).json({ status: "error", message: "Failed to fetch user profile" })
    }
  } catch (error) {
    console.error("JWT verification error:", error)
    return res.status(401).json({ status: "error", message: "Invalid token" })
  }
}

// Admin role check middleware
const adminRoleCheck = (req, res, next) => {
  // Health check endpoint doesn't require admin role
  if (req.path === "/health") {
    return next()
  }

  console.log("User in request:", JSON.stringify(req.user, null, 2))
  console.log("User role:", req.user?.role)
  console.log("User verified_admin:", req.user?.verified_admin)

  // Accept both "admin" role and verified_admin flag
  if (!req.user || (req.user.role !== "admin" && req.user.verified_admin !== true)) {
    console.log("Admin access denied: User doesn't have admin role or verified_admin flag")
    return res.status(403).json({ status: "error", message: "Admin access required" })
  }

  console.log("Admin access granted")
  next()
}

// Apply middleware to all routes
app.use(authenticateToken)
app.use(adminRoleCheck)

// Get tunnel details to use for DNS records
async function getTunnelDetails() {
  const { data } = await cloudflareAPI.get(`/accounts/${ACCOUNT_ID}/cfd_tunnel/${TUNNEL_ID}`)

  if (!data.success) {
    throw new Error("Failed to fetch tunnel details")
  }

  return data.result
}

// Create a DNS CNAME record for the hostname
async function createDNSRecord(hostname, tunnelDomain) {
  // Extract the subdomain from the full hostname
  const domainParts = hostname.split(".")
  const rootDomain = domainParts.slice(-2).join(".")
  const subdomain = domainParts.slice(0, -2).join(".")

  console.log(`Creating DNS record: ${subdomain} -> ${tunnelDomain}`)

  const { data } = await cloudflareAPI.post(`/zones/${ZONE_ID}/dns_records`, {
    type: "CNAME",
    name: subdomain,
    content: tunnelDomain,
    ttl: 1, // Auto TTL
    proxied: true,
  })

  return data
}

// API Routes for Next.js integration
// Get all tunnels with pagination
app.get("/api/tunnels", async (req, res) => {
  try {
    const page = Number.parseInt(req.query.page) || 1
    const limit = Number.parseInt(req.query.limit) || 10

    // Get tunnel configuration
    const { data: tunnelData } = await cloudflareAPI.get(
      `/accounts/${ACCOUNT_ID}/cfd_tunnel/${TUNNEL_ID}/configurations`,
    )

    if (!tunnelData.success) {
      throw new Error("Failed to fetch configurations")
    }

    // Get DNS records
    const { data: dnsData } = await cloudflareAPI.get(`/zones/${ZONE_ID}/dns_records`)
    const dnsRecords = dnsData.success ? dnsData.result : []

    // Extract ingress rules that have hostnames (excluding catch-all)
    const allTunnels = tunnelData.result.config.ingress
      .filter((rule) => rule.hostname && rule.service !== "http_status:404")
      .map((rule) => {
        // Check if DNS record exists for this hostname
        const domainParts = rule.hostname.split(".")
        const subdomain = domainParts.slice(0, -2).join(".")
        const dnsRecord = dnsRecords.find((record) => record.name === subdomain || record.name === rule.hostname)

        return {
          id: Buffer.from(rule.hostname).toString("base64"), // Generate a stable ID from hostname
          hostname: rule.hostname,
          service: rule.service,
          dns_status: dnsRecord ? "active" : "missing",
          dns_record: dnsRecord ? `CNAME ${dnsRecord.name} → ${dnsRecord.content}` : null,
          created_by: req.user?.sub || "system",
        }
      })

    // Implement pagination
    const startIndex = (page - 1) * limit
    const endIndex = page * limit
    const paginatedTunnels = allTunnels.slice(startIndex, endIndex)

    res.json({
      status: "success",
      tunnels: paginatedTunnels,
      results: allTunnels.length,
    })
  } catch (error) {
    console.error("Error fetching tunnels:", error.response?.data || error.message)
    res.status(500).json({
      status: "error",
      message: error.response?.data?.errors?.[0]?.message || error.message,
    })
  }
})

// Create a new tunnel
app.post("/api/tunnels", async (req, res) => {
  try {
    const { hostname, service } = req.body

    if (!hostname || !service) {
      return res.status(400).json({
        status: "error",
        message: "Hostname and service are required",
      })
    }

    // Get tunnel details
    const tunnelDetails = await getTunnelDetails()
    const tunnelDomain = `${TUNNEL_ID}.cfargotunnel.com`

    // Get current tunnel configuration
    const { data: currentConfigData } = await cloudflareAPI.get(
      `/accounts/${ACCOUNT_ID}/cfd_tunnel/${TUNNEL_ID}/configurations`,
    )

    if (!currentConfigData.success) {
      throw new Error("Failed to fetch current configuration")
    }

    const currentConfig = currentConfigData.result.config

    // Check if hostname already exists
    if (currentConfig.ingress.some((rule) => rule.hostname === hostname)) {
      return res.status(400).json({
        status: "error",
        message: `Tunnel with hostname ${hostname} already exists`,
      })
    }

    // Remove the catch-all rule
    const updatedIngress = currentConfig.ingress.filter((rule) => rule.service !== "http_status:404")

    // Add the new route
    updatedIngress.push({ hostname, service })

    // Add the catch-all rule back at the end
    updatedIngress.push({ service: "http_status:404" })

    // Update the tunnel configuration
    const { data: updateResponse } = await cloudflareAPI.put(
      `/accounts/${ACCOUNT_ID}/cfd_tunnel/${TUNNEL_ID}/configurations`,
      {
        config: {
          ingress: updatedIngress,
        },
      },
    )

    if (!updateResponse.success) {
      throw new Error("Failed to update tunnel configuration")
    }

    // Create DNS record
    const domainParts = hostname.split(".")
    const subdomain = domainParts.slice(0, -2).join(".")

    let dnsResponse
    try {
      dnsResponse = await createDNSRecord(hostname, tunnelDomain)
    } catch (dnsError) {
      console.error("Error creating DNS record:", dnsError.response?.data || dnsError.message)
      // Continue even if DNS creation fails
    }

    const newTunnel = {
      id: Buffer.from(hostname).toString("base64"),
      hostname,
      service,
      dns_status: dnsResponse?.success ? "active" : "missing",
      dns_record: dnsResponse?.success ? `CNAME ${dnsResponse.result.name} → ${dnsResponse.result.content}` : null,
      created_by: req.user?.sub || "system",
    }

    res.status(201).json({
      status: "success",
      data: {
        tunnel: newTunnel,
      },
    })
  } catch (error) {
    console.error("Error creating tunnel:", error.response?.data || error.message)
    res.status(500).json({
      status: "error",
      message: error.response?.data?.errors?.[0]?.message || error.message,
    })
  }
})

// Delete a tunnel
app.delete("/api/tunnels/:id", async (req, res) => {
  try {
    const { id } = req.params

    // Decode the base64 ID back to hostname
    const hostname = Buffer.from(id, "base64").toString()

    // Get current tunnel configuration
    const { data: currentConfigData } = await cloudflareAPI.get(
      `/accounts/${ACCOUNT_ID}/cfd_tunnel/${TUNNEL_ID}/configurations`,
    )

    if (!currentConfigData.success) {
      throw new Error("Failed to fetch current configuration")
    }

    const currentConfig = currentConfigData.result.config

    // Check if hostname exists
    if (!currentConfig.ingress.some((rule) => rule.hostname === hostname)) {
      return res.status(404).json({ status: "error", message: "Tunnel not found" })
    }

    // Remove the tunnel from the ingress rules
    const updatedIngress = currentConfig.ingress.filter((rule) => rule.hostname !== hostname)

    // Make sure we keep the catch-all rule
    if (!updatedIngress.some((rule) => rule.service === "http_status:404")) {
      updatedIngress.push({ service: "http_status:404" })
    }

    // Update the tunnel configuration
    const { data: updateResponse } = await cloudflareAPI.put(
      `/accounts/${ACCOUNT_ID}/cfd_tunnel/${TUNNEL_ID}/configurations`,
      {
        config: {
          ingress: updatedIngress,
        },
      },
    )

    if (!updateResponse.success) {
      throw new Error("Failed to update tunnel configuration")
    }

    // Try to find and delete the DNS record
    try {
      // Extract the subdomain
      const domainParts = hostname.split(".")
      const subdomain = domainParts.slice(0, -2).join(".")

      // Find the DNS record
      const { data: dnsData } = await cloudflareAPI.get(`/zones/${ZONE_ID}/dns_records?name=${subdomain}`)

      if (dnsData.success && dnsData.result.length > 0) {
        await cloudflareAPI.delete(`/zones/${ZONE_ID}/dns_records/${dnsData.result[0].id}`)
      }
    } catch (dnsError) {
      console.error("Error deleting DNS record:", dnsError.response?.data || dnsError.message)
      // Continue even if DNS deletion fails
    }

    res.json({
      status: "success",
      message: "Tunnel deleted successfully",
    })
  } catch (error) {
    console.error("Error deleting tunnel:", error.response?.data || error.message)
    res.status(500).json({
      status: "error",
      message: error.response?.data?.errors?.[0]?.message || error.message,
    })
  }
})

// Health check endpoint (no auth required)
app.get("/health", (req, res) => {
  res.status(200).json({ status: "ok" })
})

const PORT = process.env.PORT || 1900
app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`)
  console.log(`- API endpoints: http://localhost:${PORT}/api/tunnels`)
  console.log(`- Health check: http://localhost:${PORT}/health`)
})

module.exports = app // For testing

