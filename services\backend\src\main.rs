use std::sync::Arc;

use axum::http::{header::{ACCEPT, AUTHORIZATION, CACHE_CONTROL, CONTENT_TYPE}, HeaderValue, Method};
use dotenv::dotenv;
use sqlx::postgres::PgPoolOptions;
use tower_http::cors::CorsLayer;
use tracing_subscriber::filter::LevelFilter;

// Import from the library
use lola_backend::{
    config::Config,
    db::DBClient,
    routes::create_router,
    AppState,
};

#[tokio::main]
async fn main() {
    tracing_subscriber::fmt()
    .with_max_level(LevelFilter::DEBUG)
    .init();

    dotenv().ok();

    let config = Config::init();

    let pool = match PgPoolOptions::new()
            .max_connections(10)
            .connect(&config.database_url)
            .await
    {
        Ok(pool) => {
            println!("✅Connection to the database is successful!");
            pool
        }
        Err(err) => {
            println!("🔥 Failed to connect to the database: {:?}", err);
            std::process::exit(1);
        }
    };

    let cors = CorsLayer::new()
    .allow_origin([
        "http://localhost:3000".parse::<HeaderValue>().unwrap(),
        "http://localhost:3001".parse::<HeaderValue>().unwrap(),
        "http://localhost:3002".parse::<HeaderValue>().unwrap(),
        
        "http://localhost:8081".parse::<HeaderValue>().unwrap(), // ✅ Added 127.0.0.1:8080
        "http://web:3000".parse::<HeaderValue>().unwrap(),
        "http://box.local".parse::<HeaderValue>().unwrap(),
        "https://saday.xyz".parse::<HeaderValue>().unwrap(),
        "https://api.saday.xyz".parse::<HeaderValue>().unwrap(),
        "https://tunnels.saday.xyz".parse::<HeaderValue>().unwrap(),
    ])
    .allow_headers([AUTHORIZATION, ACCEPT, CONTENT_TYPE, CACHE_CONTROL])
    .allow_credentials(true)
    .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE, Method::OPTIONS]);

    let db_client = DBClient::new(pool);
    let app_state = AppState {
        env: config.clone(),
        db_client,
    };

    let app = create_router(Arc::new(app_state.clone())).layer(cors.clone());


    println!(
        "{}",
        format!("🚀 Server is running on http://localhost:{}", config.port)
    );

    let listener = tokio::net::TcpListener::bind(format!("0.0.0.0:{}", &config.port))
    .await
    .unwrap();

    axum::serve(listener, app).await.unwrap();
}
