{"db_name": "PostgreSQL", "query": "\n            INSERT INTO organization_members (organization_id, user_id, role, invited_by, joined_at)\n            VALUES ($1, $2, $3, $4, NOW())\n            RETURNING id, organization_id, user_id, role as \"role: OrganizationRole\", invited_by, joined_at, settings, created_at, updated_at\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "organization_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "user_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 3, "name": "role: OrganizationRole", "type_info": {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}}, {"ordinal": 4, "name": "invited_by", "type_info": "<PERSON><PERSON>"}, {"ordinal": 5, "name": "joined_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "settings", "type_info": "Jsonb"}, {"ordinal": 7, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 8, "name": "updated_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "<PERSON><PERSON>", {"Custom": {"name": "organization_role", "kind": {"Enum": ["admin", "editor", "agent", "user", "viewer"]}}}, "<PERSON><PERSON>"]}, "nullable": [false, false, false, false, true, false, true, false, false]}, "hash": "7e55339a3f4b828f6c46165c1ff346d05b860ea630716ffa036ee3cc25c8bf7f"}