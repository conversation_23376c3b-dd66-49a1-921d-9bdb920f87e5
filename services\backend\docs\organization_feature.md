# Organization Feature Documentation

## Overview

The organization feature allows users to create and manage organizations, invite team members with different roles, and collaborate effectively. This document provides a comprehensive guide to using the organization feature, from sign-up to advanced organization management.

## User Journey

### 1. Sign-Up and Authentication

#### 1.1 New User Registration
1. User enters their email address
2. System sends an OTP (One-Time Password) to the user's email
3. User verifies their email by entering the OTP
4. Upon successful verification, the user is authenticated and receives a JWT token

#### 1.2 Returning User Authentication
1. User enters their email address
2. System sends an OTP to the user's email
3. User enters the OTP to authenticate
4. Upon successful verification, the user is authenticated and receives a JWT token

### 2. Organization Creation and Management

#### 2.1 Creating a New Organization
1. After authentication, if the user doesn't have any organizations, they are prompted to create one
2. User enters the organization name and optional details (description, domain)
3. <PERSON> creates the organization and automatically assigns the user as an OrgAdmin
4. The newly created organization becomes the user's default organization

#### 2.2 Viewing Organizations
1. User can view all organizations they are a member of
2. For each organization, basic information is displayed (name, description, role)
3. User can select an organization to view more details or manage it

#### 2.3 Setting Default Organization
1. User can set any of their organizations as the default
2. The default organization is automatically selected when the user logs in

### 3. Team Member Management

#### 3.1 Inviting Team Members
1. Organization admins and editors can invite new members by email
2. When inviting, the admin/editor specifies the role for the new member
3. System sends an invitation email to the specified address
4. The invitation includes a link to accept the invitation

#### 3.2 Accepting Invitations
1. Invited user receives an email with an invitation link
2. If the user doesn't have an account, they need to create one first
3. After authentication, the user can accept the invitation
4. Upon acceptance, the user becomes a member of the organization with the assigned role

#### 3.3 Managing Team Members
1. Organization admins can view all members of the organization
2. Admins can update member roles (promote or demote)
3. Admins can remove members from the organization

### 4. Role-Based Access Control

#### 4.1 Organization Roles
- **OrgAdmin**: Full access to the organization, can manage members and settings
- **OrgEditor**: Can edit organization content and invite members
- **OrgAgent**: Can perform agent-specific actions
- **OrgUser**: Standard user with basic access
- **OrgViewer**: Read-only access to the organization

#### 4.2 Role Permissions
| Action | OrgAdmin | OrgEditor | OrgAgent | OrgUser | OrgViewer |
|--------|----------|-----------|----------|---------|-----------|
| View Organization | ✅ | ✅ | ✅ | ✅ | ✅ |
| Edit Organization | ✅ | ✅ | ❌ | ❌ | ❌ |
| Delete Organization | ✅ | ❌ | ❌ | ❌ | ❌ |
| Invite Members | ✅ | ✅ | ❌ | ❌ | ❌ |
| Manage Members | ✅ | ❌ | ❌ | ❌ | ❌ |
| Create Content | ✅ | ✅ | ✅ | ✅ | ❌ |
| Edit Content | ✅ | ✅ | ✅ | ✅ | ❌ |
| View Content | ✅ | ✅ | ✅ | ✅ | ✅ |

## API Endpoints

### Organization Endpoints

#### Create Organization
- **Endpoint**: `POST /api/organizations`
- **Auth Required**: Yes
- **Body**:
  ```json
  {
    "name": "Organization Name",
    "description": "Optional description",
    "domain": "Optional domain"
  }
  ```
- **Response**:
  ```json
  {
    "status": "success",
    "data": {
      "organization": {
        "id": "uuid",
        "name": "Organization Name",
        "description": "Optional description",
        "domain": "Optional domain",
        "createdAt": "timestamp",
        "updatedAt": "timestamp"
      }
    }
  }
  ```

#### Get User Organizations
- **Endpoint**: `GET /api/organizations`
- **Auth Required**: Yes
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10)
- **Response**:
  ```json
  {
    "status": "success",
    "organizations": [
      {
        "id": "uuid",
        "name": "Organization Name",
        "description": "Optional description",
        "domain": "Optional domain",
        "createdAt": "timestamp",
        "updatedAt": "timestamp"
      }
    ],
    "results": 1
  }
  ```

#### Get Organization Details
- **Endpoint**: `GET /api/organizations/:id`
- **Auth Required**: Yes
- **Response**:
  ```json
  {
    "status": "success",
    "data": {
      "organization": {
        "id": "uuid",
        "name": "Organization Name",
        "description": "Optional description",
        "domain": "Optional domain",
        "createdAt": "timestamp",
        "updatedAt": "timestamp"
      }
    }
  }
  ```

### Member Management Endpoints

#### Invite Member
- **Endpoint**: `POST /api/organizations/:id/members`
- **Auth Required**: Yes
- **Body**:
  ```json
  {
    "email": "<EMAIL>",
    "role": "user"
  }
  ```
- **Response**:
  ```json
  {
    "status": "success",
    "message": "Invitation <NAME_EMAIL>"
  }
  ```

#### Get Organization Members
- **Endpoint**: `GET /api/organizations/:id/members`
- **Auth Required**: Yes
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10)
- **Response**:
  ```json
  {
    "status": "success",
    "members": [
      {
        "id": "uuid",
        "organization_id": "uuid",
        "user_id": "uuid",
        "role": "admin",
        "joined_at": "timestamp",
        "user_name": "User Name",
        "user_email": "<EMAIL>"
      }
    ],
    "results": 1
  }
  ```

#### Update Member Role
- **Endpoint**: `PUT /api/organizations/:id/members/:member_id`
- **Auth Required**: Yes
- **Body**:
  ```json
  {
    "role": "editor"
  }
  ```
- **Response**:
  ```json
  {
    "status": "success",
    "data": {
      "member": {
        "id": "uuid",
        "organization_id": "uuid",
        "user_id": "uuid",
        "role": "editor",
        "joined_at": "timestamp",
        "user_name": "User Name",
        "user_email": "<EMAIL>"
      }
    }
  }
  ```

#### Remove Member
- **Endpoint**: `DELETE /api/organizations/:id/members/:member_id`
- **Auth Required**: Yes
- **Response**:
  ```json
  {
    "status": "success",
    "message": "Member removed successfully"
  }
  ```

### Invitation Endpoints

#### Accept Invitation
- **Endpoint**: `POST /api/organizations/invitations/accept`
- **Auth Required**: Yes
- **Body**:
  ```json
  {
    "token": "invitation_token"
  }
  ```
- **Response**:
  ```json
  {
    "status": "success",
    "message": "You have joined Organization Name"
  }
  ```

#### Get User Invitations
- **Endpoint**: `GET /api/organizations/invitations`
- **Auth Required**: Yes
- **Response**:
  ```json
  [
    {
      "id": "uuid",
      "organization_id": "uuid",
      "email": "<EMAIL>",
      "role": "user",
      "invited_by": "uuid",
      "token": "invitation_token",
      "expires_at": "timestamp",
      "status": "pending",
      "organization_name": "Organization Name",
      "invited_by_name": "Inviter Name"
    }
  ]
  ```

## Best Practices

1. **Role Assignment**: Assign the minimum necessary role to each team member
2. **Regular Audits**: Periodically review organization members and their roles
3. **Secure Invitations**: Ensure invitation links are only shared with intended recipients
4. **Default Organization**: Set your most frequently used organization as the default

## Troubleshooting

### Common Issues

1. **Invitation Not Received**
   - Check spam/junk folder
   - Verify the email address is correct
   - Ask the admin to resend the invitation

2. **Cannot Accept Invitation**
   - Ensure you're logged in with the email address the invitation was sent to
   - Check if the invitation has expired (valid for 7 days)
   - Contact the organization admin for a new invitation

3. **Permission Denied**
   - Verify your role in the organization
   - Contact the organization admin if you need additional permissions

## Support

For additional help or to report issues, please contact <NAME_EMAIL>.
