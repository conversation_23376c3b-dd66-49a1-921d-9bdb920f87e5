#!/bin/bash

# Colors for terminal output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message "${BLUE}" "=== Token Debug Tool ==="
print_message "${YELLOW}" "This tool will help debug JWT token issues"

# Ask for the token
print_message "${BLUE}" "Please paste your JWT token:"
read -p "Token: " TOKEN

if [ -z "$TOKEN" ]; then
    print_message "${RED}" "No token provided. Exiting."
    exit 1
fi

# Check token format
if [[ ! $TOKEN =~ ^[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+$ ]]; then
    print_message "${RED}" "Token does not appear to be a valid JWT (should have 3 parts separated by dots)"
    exit 1
fi

# Extract token parts
HEADER=$(echo $TOKEN | cut -d. -f1)
PAYLOAD=$(echo $TOKEN | cut -d. -f2)
SIGNATURE=$(echo $TOKEN | cut -d. -f3)

print_message "${GREEN}" "Token appears to be a valid JWT format"
print_message "${YELLOW}" "Token length: ${#TOKEN}"
print_message "${YELLOW}" "Header length: ${#HEADER}"
print_message "${YELLOW}" "Payload length: ${#PAYLOAD}"
print_message "${YELLOW}" "Signature length: ${#SIGNATURE}"

# Decode header and payload
print_message "${BLUE}" "Decoding header..."
HEADER_DECODED=$(echo $HEADER | base64 -d 2>/dev/null || echo $HEADER | base64 -d -i 2>/dev/null)
print_message "${GREEN}" "Header: $HEADER_DECODED"

print_message "${BLUE}" "Decoding payload..."
PAYLOAD_DECODED=$(echo $PAYLOAD | base64 -d 2>/dev/null || echo $PAYLOAD | base64 -d -i 2>/dev/null)
print_message "${GREEN}" "Payload: $PAYLOAD_DECODED"

# Extract expiration time
EXP=$(echo $PAYLOAD_DECODED | grep -o '"exp":[0-9]*' | cut -d: -f2)
if [ ! -z "$EXP" ]; then
    CURRENT_TIME=$(date +%s)
    print_message "${YELLOW}" "Current time (epoch): $CURRENT_TIME"
    print_message "${YELLOW}" "Token expiration (epoch): $EXP"
    
    if [ $CURRENT_TIME -gt $EXP ]; then
        print_message "${RED}" "Token has expired!"
    else
        TIME_LEFT=$((EXP - CURRENT_TIME))
        MINUTES=$((TIME_LEFT / 60))
        SECONDS=$((TIME_LEFT % 60))
        print_message "${GREEN}" "Token is still valid for $MINUTES minutes and $SECONDS seconds"
    fi
fi

# Extract subject
SUB=$(echo $PAYLOAD_DECODED | grep -o '"sub":"[^"]*' | cut -d'"' -f4)
if [ ! -z "$SUB" ]; then
    print_message "${YELLOW}" "Token subject (user ID): $SUB"
fi

# Test token with API
print_message "${BLUE}" "Testing token with API..."

# Use the local server on port 8001
API_URL="http://localhost:8001/api"

# Test with /users/me endpoint
print_message "${YELLOW}" "Testing with /users/me endpoint..."
ME_RESPONSE=$(curl -s -X GET "${API_URL}/users/me" \
    -H "Authorization: Bearer ${TOKEN}" \
    --cookie "token=${TOKEN}")
print_message "${GREEN}" "Response: $ME_RESPONSE"

# Test with /organizations endpoint
print_message "${YELLOW}" "Testing with /organizations endpoint..."
ORG_RESPONSE=$(curl -s -X GET "${API_URL}/organizations" \
    -H "Authorization: Bearer ${TOKEN}" \
    --cookie "token=${TOKEN}")
print_message "${GREEN}" "Response: $ORG_RESPONSE"

print_message "${BLUE}" "=== Debug Completed ==="
