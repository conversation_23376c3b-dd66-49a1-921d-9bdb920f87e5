# Environment Setup

This guide provides instructions for setting up the development environment for the Saday Agent platform.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Rust** (latest stable version)
- **Node.js** (v18 or later)
- **pnpm** (v8 or later)
- **PostgreSQL** (v15 or later)
- **Docker** and **Docker Compose** (for containerized development)
- **Git**

## Backend Setup

### 1. Install Rust and Cargo

If you don't have Rust installed, use [rustup](https://rustup.rs/):

```bash
# Windows (PowerShell)
Invoke-WebRequest -Uri https://win.rustup.rs -OutFile rustup-init.exe
.\rustup-init.exe

# macOS/Linux
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

### 2. Install PostgreSQL

#### Option 1: Local Installation

- **Windows**: Download and install from [PostgreSQL website](https://www.postgresql.org/download/windows/)
- **macOS**: Use Homebrew: `brew install postgresql@15`
- **Linux**: Use your distribution's package manager, e.g., `sudo apt install postgresql-15`

#### Option 2: Docker (Recommended)

Use the provided Docker Compose configuration:

```bash
# Start PostgreSQL container
docker-compose up -d db
```

### 3. Database Configuration

Create a database for development:

```bash
# Connect to PostgreSQL
psql -U postgres

# Create database
CREATE DATABASE sadayagent;

# Exit
\q
```

### 4. Backend Environment Variables

Create a `.env` file in the `services/backend` directory:

```
DATABASE_URL=postgres://postgres:postgres@localhost:5432/sadayagent
JWT_SECRET_KEY=your_jwt_secret_key
JWT_MAXAGE=60
SMTP_SERVER=your_smtp_server
SMTP_PORT=587
SMTP_USERNAME=your_smtp_username
SMTP_PASSWORD=your_smtp_password
SMTP_FROM_ADDRESS=<EMAIL>
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8001
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
```

Replace the placeholder values with your actual configuration.

### 5. Run Database Migrations

```bash
cd services/backend
cargo install sqlx-cli
sqlx migrate run
```

## Frontend Setup

### 1. Install Node.js and pnpm

- **Node.js**: Download and install from [Node.js website](https://nodejs.org/)
- **pnpm**: Install globally using npm:

```bash
npm install -g pnpm
```

### 2. UI Library Setup

```bash
# From the project root
pnpm install
pnpm build:ui
```

This builds the shared UI component library (ApatiteUI) that is used by both web and mobile frontends.

### 3. Web Frontend Setup

```bash
# Navigate to the web frontend directory
cd agent-web

# Install dependencies
pnpm install

# Create .env.local file
echo "NEXT_PUBLIC_API_URL=http://localhost:8001/api" > .env.local
```

### 4. Mobile Frontend Setup

```bash
# Navigate to the mobile frontend directory
cd agent-native

# Install dependencies
pnpm install
```

## Docker Setup (Optional)

For a fully containerized development environment:

1. Install Docker and Docker Compose from [Docker website](https://www.docker.com/get-started)

2. Build and start all services:

```bash
# From the project root
docker-compose up -d
```

This will start:
- PostgreSQL database
- Backend API server
- Web frontend
- Additional services (if configured)

## Azure PostgreSQL Setup (Production)

For production environments, we use Azure PostgreSQL:

1. Create an Azure PostgreSQL server:
   - Server name: sadayagent
   - Region: Central India
   - Username: backend
   - Password: SadayAgent@2069 (use a more secure password in actual production)

2. Configure firewall rules to allow connections from your application servers

3. Update the production environment variables to use the Azure PostgreSQL connection string:

```
DATABASE_URL=******************************************/sadayagent?sslmode=require
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Verify PostgreSQL is running: `pg_isready`
   - Check connection string in `.env` file
   - Ensure firewall allows connections to PostgreSQL port (5432)

2. **Rust Build Errors**:
   - Update Rust: `rustup update`
   - Clean build: `cargo clean && cargo build`

3. **Node.js/pnpm Issues**:
   - Clear cache: `pnpm store prune`
   - Reinstall dependencies: `rm -rf node_modules && pnpm install`

4. **Docker Issues**:
   - Check container status: `docker-compose ps`
   - View logs: `docker-compose logs -f [service_name]`
   - Rebuild containers: `docker-compose build --no-cache`

## Next Steps

After setting up your environment:

1. Proceed to [Local Development](./local-development.md) for development workflow instructions
2. Review the [API Reference](./api-reference.md) for API documentation
3. Check the [Authentication System](./authentication.md) documentation for details on the authentication flow
