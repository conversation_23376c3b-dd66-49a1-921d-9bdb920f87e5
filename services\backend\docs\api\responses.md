# Response Format

The Saday Backend API uses a consistent response format for all endpoints. This document describes the standard response formats and provides examples.

## Success Responses

All successful responses include a `status` field with the value `"success"` and either a `data` field containing the response data or a `message` field containing a success message.

### Success with Data

```json
{
  "status": "success",
  "data": {
    // Response data
  }
}
```

Example (Get User):

```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "9669208d-b352-4258-bf59-e9b4a543fa16",
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "role": "user",
      "verified": true,
      "verified_admin": false,
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  }
}
```

### Success with Message

```json
{
  "status": "success",
  "message": "Success message"
}
```

Example (Delete Organization):

```json
{
  "status": "success",
  "message": "Organization deleted successfully"
}
```

### Success with List Data

For endpoints that return lists of items, the response includes a `results` field with the total number of items.

```json
{
  "status": "success",
  "items": [
    // Array of items
  ],
  "results": 10 // Total number of items
}
```

Example (Get Organizations):

```json
{
  "status": "success",
  "organizations": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "My Organization",
      "description": "This is my organization",
      "domain": "example.com",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    },
    {
      "id": "123e4567-e89b-12d3-a456-426614174001",
      "name": "Another Organization",
      "description": "This is another organization",
      "domain": "another-example.com",
      "createdAt": "2023-01-02T00:00:00Z",
      "updatedAt": "2023-01-02T00:00:00Z"
    }
  ],
  "results": 2
}
```

## Error Responses

All error responses include a `status` field with the value `"error"` and a `message` field containing an error message.

```json
{
  "status": "error",
  "message": "Error message"
}
```

Example (Invalid Input):

```json
{
  "status": "error",
  "message": "Invalid email format"
}
```

Example (Authentication Error):

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

Example (Permission Error):

```json
{
  "status": "error",
  "message": "Permission denied. You do not have access to this resource."
}
```

Example (Resource Not Found):

```json
{
  "status": "error",
  "message": "Organization not found"
}
```

Example (Rate Limit Exceeded):

```json
{
  "status": "error",
  "message": "Too many OTP requests. Please try again later."
}
```

Example (Server Error):

```json
{
  "status": "error",
  "message": "An unexpected error occurred. Please try again later."
}
```

## HTTP Status Codes

The API uses standard HTTP status codes to indicate the success or failure of a request:

- `200 OK`: The request was successful
- `201 Created`: A new resource was successfully created
- `400 Bad Request`: The request was invalid or cannot be served
- `401 Unauthorized`: Authentication is required and has failed or has not been provided
- `403 Forbidden`: The authenticated user does not have access to the requested resource
- `404 Not Found`: The requested resource could not be found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: An error occurred on the server

## Pagination

List endpoints support pagination with the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10, max: 50)

Example Request:

```
GET /api/organizations?page=2&limit=20
```

Example Response:

```json
{
  "status": "success",
  "organizations": [
    // Array of organizations (20 items)
  ],
  "results": 45 // Total number of items
}
```

## Response Examples

For more examples of API responses, see the [API Examples](./examples/) directory.
