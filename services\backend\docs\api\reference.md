# API Reference

This document provides a comprehensive reference for all API endpoints in the Saday Backend.

## Base URL

All API endpoints are prefixed with:

```
https://api.saday.xyz/api
```

## Authentication

Most endpoints require authentication via JWT token. The token can be provided in two ways:

1. As a cookie (set automatically after login)
2. In the Authorization header: `Authorization: Bearer YOUR_JWT_TOKEN`

For more details, see the [Authentication](./authentication.md) documentation.

## Endpoints

### Health Check Endpoint

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| GET | `/healthchecker` | Check if the API is running | No |

#### Response

```json
{
  "status": "success",
  "message": "API is running"
}
```

### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|--------------|
| POST | `/auth/request-otp` | Request an OTP for authentication | No |
| POST | `/auth/verify-otp` | Verify OTP and authenticate | No |
| POST | `/auth/resend-otp` | Resend OTP to email | No |
| POST | `/auth/reset-account` | Request account reset | No |
| POST | `/auth/verify-reset` | Verify account reset | No |

For detailed information, see the [Authentication API](./authentication.md) documentation.

### User Endpoints

| Method | Endpoint | Description | Auth Required | Permissions |
|--------|----------|-------------|--------------|-------------|
| GET | `/users/me` | Get current user information | Yes | Any authenticated user |
| GET | `/users/users` | Get all users | Yes | Admin only |
| PUT | `/users/profile` | Update user profile | Yes | Any authenticated user |
| PUT | `/users/role` | Update user role | Yes | Admin only |

For detailed information, see the [Users API](./users.md) documentation.

### Organization Endpoints

| Method | Endpoint | Description | Auth Required | Permissions |
|--------|----------|-------------|--------------|-------------|
| POST | `/organizations` | Create a new organization | Yes | Any authenticated user |
| GET | `/organizations` | Get user organizations | Yes | Any authenticated user |
| GET | `/organizations/:id` | Get organization details | Yes | Member of the organization |
| PUT | `/organizations/:id` | Update organization | Yes | OrgAdmin or OrgEditor |
| DELETE | `/organizations/:id` | Delete organization | Yes | OrgAdmin |
| GET | `/organizations/:id/members` | Get organization members | Yes | Member of the organization |
| POST | `/organizations/:id/members` | Invite member to organization | Yes | OrgAdmin or OrgEditor |
| PUT | `/organizations/:id/members/:member_id` | Update member role | Yes | OrgAdmin |
| DELETE | `/organizations/:id/members/:member_id` | Remove member from organization | Yes | OrgAdmin |
| POST | `/organizations/invitations/accept` | Accept organization invitation | Yes | Any authenticated user |
| GET | `/organizations/invitations` | Get user invitations | Yes | Any authenticated user |
| GET | `/organizations/:id/invitations` | Get organization invitations | Yes | OrgAdmin or OrgEditor |
| PUT | `/organizations/default/:id` | Set default organization | Yes | Member of the organization |

For detailed information, see the [Organization API](./organization.md) documentation.

## Response Format

All API responses follow a consistent format:

### Success Response

```json
{
  "status": "success",
  "data": { ... } // or message: "Success message"
}
```

#### Examples

Success with data:

```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "9669208d-b352-4258-bf59-e9b4a543fa16",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "verified": true,
      "verified_admin": false,
      "profile_data": {
        "bio": "Software developer",
        "location": "San Francisco"
      },
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  }
}
```

Success with message:

```json
{
  "status": "success",
  "message": "Organization deleted successfully"
}
```

Success with list data:

```json
{
  "status": "success",
  "organizations": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "My Organization",
      "description": "This is my organization",
      "domain": "example.com",
      "settings": {
        "color": "#FF5733",
        "logo": "https://example.com/logo.png"
      },
      "role": "orgadmin",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  ],
  "results": 1
}
```

### Error Response

```json
{
  "status": "error",
  "message": "Error description"
}
```

#### Examples

Authentication error:

```json
{
  "status": "error",
  "message": "Authentication required. Please log in."
}
```

Validation error:

```json
{
  "status": "error",
  "message": "Invalid email format"
}
```

Resource not found:

```json
{
  "status": "error",
  "message": "Organization not found"
}
```

For more details, see the [Response Format](./responses.md) documentation.

## Pagination

List endpoints support pagination with the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10, max: 50)

Example:

```
GET /api/organizations?page=2&limit=20
```

## Error Codes

The API uses standard HTTP status codes to indicate the success or failure of a request:

- `200 OK`: The request was successful
- `201 Created`: A new resource was successfully created
- `400 Bad Request`: The request was invalid or cannot be served
- `401 Unauthorized`: Authentication is required and has failed or has not been provided
- `403 Forbidden`: The authenticated user does not have access to the requested resource
- `404 Not Found`: The requested resource could not be found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: An error occurred on the server

For more details, see the [Error Handling](./errors.md) documentation.

## Postman Collection

A Postman collection is available for testing the API endpoints. You can download it [here](./saday_postman_collection.json).
