# Display menu
Write-Host "=== Authentication Tests ==="
Write-Host "1. Run signup test"
Write-Host "2. Run signin test"
Write-Host "3. Exit"
Write-Host ""

$choice = Read-Host "Enter your choice (1-3)"

switch ($choice) {
    "1" {
        Write-Host "Running signup test..."
        cargo test test_signup_flow -- --ignored --nocapture
    }
    "2" {
        Write-Host "Running signin test..."
        cargo test test_signin_flow -- --ignored --nocapture
    }
    "3" {
        Write-Host "Exiting..."
        exit
    }
    default {
        Write-Host "Invalid choice. Exiting..."
        exit
    }
}

Write-Host "Test completed."
